import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

class RefreshIndicatorWidget extends StatelessWidget {
  const RefreshIndicatorWidget({
    super.key,
    required this.countdown,
    required this.isOnline,
    this.onRefresh,
  });

  final int countdown;
  final bool isOnline;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onRefresh,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isOnline ? AppColors.successLight : AppColors.errorLight,
          border: Border.all(
            color: isOnline ? AppColors.success : AppColors.error,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isOnline ? Icons.wifi : Icons.wifi_off,
              size: 16,
              color: isOnline ? AppColors.success : AppColors.error,
            ),
            const SizedBox(width: 8),
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 300),
              tween: Tween(begin: 0, end: countdown <= 1 && isOnline ? 1 : 0),
              builder: (context, value, child) {
                return Transform.rotate(
                  angle: value * 2 * 3.14159,
                  child: Icon(
                    Icons.refresh,
                    size: 16,
                    color: isOnline ? AppColors.success : AppColors.error,
                  ),
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              isOnline ? 'Cập nhật sau ${countdown}s' : 'Mất kết nối',
              style: AppTextStyles.labelMedium.copyWith(
                color: isOnline ? AppColors.success : AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (isOnline) ...[
              const SizedBox(width: 8),
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.gray200,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: (5 - countdown) / 5,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.success,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
