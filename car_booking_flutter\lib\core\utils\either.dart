import 'package:equatable/equatable.dart';

abstract class Either<L, R> extends Equatable {
  const Either();

  bool get isLeft => this is Left<L, R>;
  bool get isRight => this is Right<L, R>;

  L get left => (this as Left<L, R>).value;
  R get right => (this as Right<L, R>).value;

  T fold<T>(T Function(L) leftFunction, T Function(R) rightFunction) {
    if (isLeft) {
      return leftFunction(left);
    } else {
      return rightFunction(right);
    }
  }

  Either<L, T> map<T>(T Function(R) function) {
    if (isLeft) {
      return Left<L, T>(left);
    } else {
      return Right<L, T>(function(right));
    }
  }

  Either<T, R> mapLeft<T>(T Function(L) function) {
    if (isLeft) {
      return Left<T, R>(function(left));
    } else {
      return Right<T, R>(right);
    }
  }

  @override
  List<Object?> get props => [
        if (isLeft) left else right,
      ];
}

class Left<L, R> extends Either<L, R> {
  const Left(this.value);

  final L value;
}

class Right<L, R> extends Either<L, R> {
  const Right(this.value);

  final R value;
}
