// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

DriverModel _$DriverModelFromJson(Map<String, dynamic> json) {
  return _DriverModel.fromJson(json);
}

/// @nodoc
mixin _$DriverModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String? get zalo => throw _privateConstructorUsedError;
  DriverStatus get status => throw _privateConstructorUsedError;
  String? get currentLocation => throw _privateConstructorUsedError;
  String get rating => throw _privateConstructorUsedError;
  int get totalRides => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Serializes this DriverModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverModelCopyWith<DriverModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverModelCopyWith<$Res> {
  factory $DriverModelCopyWith(
    DriverModel value,
    $Res Function(DriverModel) then,
  ) = _$DriverModelCopyWithImpl<$Res, DriverModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String phone,
    String? zalo,
    DriverStatus status,
    String? currentLocation,
    String rating,
    int totalRides,
    DateTime? createdAt,
  });
}

/// @nodoc
class _$DriverModelCopyWithImpl<$Res, $Val extends DriverModel>
    implements $DriverModelCopyWith<$Res> {
  _$DriverModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phone = null,
    Object? zalo = freezed,
    Object? status = null,
    Object? currentLocation = freezed,
    Object? rating = null,
    Object? totalRides = null,
    Object? createdAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            zalo: freezed == zalo
                ? _value.zalo
                : zalo // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as DriverStatus,
            currentLocation: freezed == currentLocation
                ? _value.currentLocation
                : currentLocation // ignore: cast_nullable_to_non_nullable
                      as String?,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as String,
            totalRides: null == totalRides
                ? _value.totalRides
                : totalRides // ignore: cast_nullable_to_non_nullable
                      as int,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverModelImplCopyWith<$Res>
    implements $DriverModelCopyWith<$Res> {
  factory _$$DriverModelImplCopyWith(
    _$DriverModelImpl value,
    $Res Function(_$DriverModelImpl) then,
  ) = __$$DriverModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String phone,
    String? zalo,
    DriverStatus status,
    String? currentLocation,
    String rating,
    int totalRides,
    DateTime? createdAt,
  });
}

/// @nodoc
class __$$DriverModelImplCopyWithImpl<$Res>
    extends _$DriverModelCopyWithImpl<$Res, _$DriverModelImpl>
    implements _$$DriverModelImplCopyWith<$Res> {
  __$$DriverModelImplCopyWithImpl(
    _$DriverModelImpl _value,
    $Res Function(_$DriverModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phone = null,
    Object? zalo = freezed,
    Object? status = null,
    Object? currentLocation = freezed,
    Object? rating = null,
    Object? totalRides = null,
    Object? createdAt = freezed,
  }) {
    return _then(
      _$DriverModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        zalo: freezed == zalo
            ? _value.zalo
            : zalo // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as DriverStatus,
        currentLocation: freezed == currentLocation
            ? _value.currentLocation
            : currentLocation // ignore: cast_nullable_to_non_nullable
                  as String?,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as String,
        totalRides: null == totalRides
            ? _value.totalRides
            : totalRides // ignore: cast_nullable_to_non_nullable
                  as int,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverModelImpl implements _DriverModel {
  const _$DriverModelImpl({
    required this.id,
    required this.name,
    required this.phone,
    this.zalo,
    this.status = DriverStatus.active,
    this.currentLocation,
    this.rating = '0.00',
    this.totalRides = 0,
    this.createdAt,
  });

  factory _$DriverModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String phone;
  @override
  final String? zalo;
  @override
  @JsonKey()
  final DriverStatus status;
  @override
  final String? currentLocation;
  @override
  @JsonKey()
  final String rating;
  @override
  @JsonKey()
  final int totalRides;
  @override
  final DateTime? createdAt;

  @override
  String toString() {
    return 'DriverModel(id: $id, name: $name, phone: $phone, zalo: $zalo, status: $status, currentLocation: $currentLocation, rating: $rating, totalRides: $totalRides, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.zalo, zalo) || other.zalo == zalo) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    phone,
    zalo,
    status,
    currentLocation,
    rating,
    totalRides,
    createdAt,
  );

  /// Create a copy of DriverModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverModelImplCopyWith<_$DriverModelImpl> get copyWith =>
      __$$DriverModelImplCopyWithImpl<_$DriverModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverModelImplToJson(this);
  }
}

abstract class _DriverModel implements DriverModel {
  const factory _DriverModel({
    required final String id,
    required final String name,
    required final String phone,
    final String? zalo,
    final DriverStatus status,
    final String? currentLocation,
    final String rating,
    final int totalRides,
    final DateTime? createdAt,
  }) = _$DriverModelImpl;

  factory _DriverModel.fromJson(Map<String, dynamic> json) =
      _$DriverModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get phone;
  @override
  String? get zalo;
  @override
  DriverStatus get status;
  @override
  String? get currentLocation;
  @override
  String get rating;
  @override
  int get totalRides;
  @override
  DateTime? get createdAt;

  /// Create a copy of DriverModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverModelImplCopyWith<_$DriverModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverWithStatsModel _$DriverWithStatsModelFromJson(Map<String, dynamic> json) {
  return _DriverWithStatsModel.fromJson(json);
}

/// @nodoc
mixin _$DriverWithStatsModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String? get zalo => throw _privateConstructorUsedError;
  DriverStatus get status => throw _privateConstructorUsedError;
  String? get currentLocation => throw _privateConstructorUsedError;
  String get rating => throw _privateConstructorUsedError;
  int get totalRides => throw _privateConstructorUsedError;
  int get completedRides => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Serializes this DriverWithStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverWithStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverWithStatsModelCopyWith<DriverWithStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverWithStatsModelCopyWith<$Res> {
  factory $DriverWithStatsModelCopyWith(
    DriverWithStatsModel value,
    $Res Function(DriverWithStatsModel) then,
  ) = _$DriverWithStatsModelCopyWithImpl<$Res, DriverWithStatsModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String phone,
    String? zalo,
    DriverStatus status,
    String? currentLocation,
    String rating,
    int totalRides,
    int completedRides,
    DateTime? createdAt,
  });
}

/// @nodoc
class _$DriverWithStatsModelCopyWithImpl<
  $Res,
  $Val extends DriverWithStatsModel
>
    implements $DriverWithStatsModelCopyWith<$Res> {
  _$DriverWithStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverWithStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phone = null,
    Object? zalo = freezed,
    Object? status = null,
    Object? currentLocation = freezed,
    Object? rating = null,
    Object? totalRides = null,
    Object? completedRides = null,
    Object? createdAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            zalo: freezed == zalo
                ? _value.zalo
                : zalo // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as DriverStatus,
            currentLocation: freezed == currentLocation
                ? _value.currentLocation
                : currentLocation // ignore: cast_nullable_to_non_nullable
                      as String?,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as String,
            totalRides: null == totalRides
                ? _value.totalRides
                : totalRides // ignore: cast_nullable_to_non_nullable
                      as int,
            completedRides: null == completedRides
                ? _value.completedRides
                : completedRides // ignore: cast_nullable_to_non_nullable
                      as int,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverWithStatsModelImplCopyWith<$Res>
    implements $DriverWithStatsModelCopyWith<$Res> {
  factory _$$DriverWithStatsModelImplCopyWith(
    _$DriverWithStatsModelImpl value,
    $Res Function(_$DriverWithStatsModelImpl) then,
  ) = __$$DriverWithStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String phone,
    String? zalo,
    DriverStatus status,
    String? currentLocation,
    String rating,
    int totalRides,
    int completedRides,
    DateTime? createdAt,
  });
}

/// @nodoc
class __$$DriverWithStatsModelImplCopyWithImpl<$Res>
    extends _$DriverWithStatsModelCopyWithImpl<$Res, _$DriverWithStatsModelImpl>
    implements _$$DriverWithStatsModelImplCopyWith<$Res> {
  __$$DriverWithStatsModelImplCopyWithImpl(
    _$DriverWithStatsModelImpl _value,
    $Res Function(_$DriverWithStatsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverWithStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phone = null,
    Object? zalo = freezed,
    Object? status = null,
    Object? currentLocation = freezed,
    Object? rating = null,
    Object? totalRides = null,
    Object? completedRides = null,
    Object? createdAt = freezed,
  }) {
    return _then(
      _$DriverWithStatsModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        zalo: freezed == zalo
            ? _value.zalo
            : zalo // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as DriverStatus,
        currentLocation: freezed == currentLocation
            ? _value.currentLocation
            : currentLocation // ignore: cast_nullable_to_non_nullable
                  as String?,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as String,
        totalRides: null == totalRides
            ? _value.totalRides
            : totalRides // ignore: cast_nullable_to_non_nullable
                  as int,
        completedRides: null == completedRides
            ? _value.completedRides
            : completedRides // ignore: cast_nullable_to_non_nullable
                  as int,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverWithStatsModelImpl implements _DriverWithStatsModel {
  const _$DriverWithStatsModelImpl({
    required this.id,
    required this.name,
    required this.phone,
    this.zalo,
    this.status = DriverStatus.active,
    this.currentLocation,
    this.rating = '0.00',
    this.totalRides = 0,
    this.completedRides = 0,
    this.createdAt,
  });

  factory _$DriverWithStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverWithStatsModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String phone;
  @override
  final String? zalo;
  @override
  @JsonKey()
  final DriverStatus status;
  @override
  final String? currentLocation;
  @override
  @JsonKey()
  final String rating;
  @override
  @JsonKey()
  final int totalRides;
  @override
  @JsonKey()
  final int completedRides;
  @override
  final DateTime? createdAt;

  @override
  String toString() {
    return 'DriverWithStatsModel(id: $id, name: $name, phone: $phone, zalo: $zalo, status: $status, currentLocation: $currentLocation, rating: $rating, totalRides: $totalRides, completedRides: $completedRides, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverWithStatsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.zalo, zalo) || other.zalo == zalo) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.completedRides, completedRides) ||
                other.completedRides == completedRides) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    phone,
    zalo,
    status,
    currentLocation,
    rating,
    totalRides,
    completedRides,
    createdAt,
  );

  /// Create a copy of DriverWithStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverWithStatsModelImplCopyWith<_$DriverWithStatsModelImpl>
  get copyWith =>
      __$$DriverWithStatsModelImplCopyWithImpl<_$DriverWithStatsModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverWithStatsModelImplToJson(this);
  }
}

abstract class _DriverWithStatsModel implements DriverWithStatsModel {
  const factory _DriverWithStatsModel({
    required final String id,
    required final String name,
    required final String phone,
    final String? zalo,
    final DriverStatus status,
    final String? currentLocation,
    final String rating,
    final int totalRides,
    final int completedRides,
    final DateTime? createdAt,
  }) = _$DriverWithStatsModelImpl;

  factory _DriverWithStatsModel.fromJson(Map<String, dynamic> json) =
      _$DriverWithStatsModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get phone;
  @override
  String? get zalo;
  @override
  DriverStatus get status;
  @override
  String? get currentLocation;
  @override
  String get rating;
  @override
  int get totalRides;
  @override
  int get completedRides;
  @override
  DateTime? get createdAt;

  /// Create a copy of DriverWithStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverWithStatsModelImplCopyWith<_$DriverWithStatsModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

CreateDriverModel _$CreateDriverModelFromJson(Map<String, dynamic> json) {
  return _CreateDriverModel.fromJson(json);
}

/// @nodoc
mixin _$CreateDriverModel {
  String get name => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String? get zalo => throw _privateConstructorUsedError;
  DriverStatus get status => throw _privateConstructorUsedError;
  String? get currentLocation => throw _privateConstructorUsedError;

  /// Serializes this CreateDriverModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateDriverModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateDriverModelCopyWith<CreateDriverModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateDriverModelCopyWith<$Res> {
  factory $CreateDriverModelCopyWith(
    CreateDriverModel value,
    $Res Function(CreateDriverModel) then,
  ) = _$CreateDriverModelCopyWithImpl<$Res, CreateDriverModel>;
  @useResult
  $Res call({
    String name,
    String phone,
    String? zalo,
    DriverStatus status,
    String? currentLocation,
  });
}

/// @nodoc
class _$CreateDriverModelCopyWithImpl<$Res, $Val extends CreateDriverModel>
    implements $CreateDriverModelCopyWith<$Res> {
  _$CreateDriverModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateDriverModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phone = null,
    Object? zalo = freezed,
    Object? status = null,
    Object? currentLocation = freezed,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            zalo: freezed == zalo
                ? _value.zalo
                : zalo // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as DriverStatus,
            currentLocation: freezed == currentLocation
                ? _value.currentLocation
                : currentLocation // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CreateDriverModelImplCopyWith<$Res>
    implements $CreateDriverModelCopyWith<$Res> {
  factory _$$CreateDriverModelImplCopyWith(
    _$CreateDriverModelImpl value,
    $Res Function(_$CreateDriverModelImpl) then,
  ) = __$$CreateDriverModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String phone,
    String? zalo,
    DriverStatus status,
    String? currentLocation,
  });
}

/// @nodoc
class __$$CreateDriverModelImplCopyWithImpl<$Res>
    extends _$CreateDriverModelCopyWithImpl<$Res, _$CreateDriverModelImpl>
    implements _$$CreateDriverModelImplCopyWith<$Res> {
  __$$CreateDriverModelImplCopyWithImpl(
    _$CreateDriverModelImpl _value,
    $Res Function(_$CreateDriverModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CreateDriverModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phone = null,
    Object? zalo = freezed,
    Object? status = null,
    Object? currentLocation = freezed,
  }) {
    return _then(
      _$CreateDriverModelImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        zalo: freezed == zalo
            ? _value.zalo
            : zalo // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as DriverStatus,
        currentLocation: freezed == currentLocation
            ? _value.currentLocation
            : currentLocation // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateDriverModelImpl implements _CreateDriverModel {
  const _$CreateDriverModelImpl({
    required this.name,
    required this.phone,
    this.zalo,
    this.status = DriverStatus.active,
    this.currentLocation,
  });

  factory _$CreateDriverModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateDriverModelImplFromJson(json);

  @override
  final String name;
  @override
  final String phone;
  @override
  final String? zalo;
  @override
  @JsonKey()
  final DriverStatus status;
  @override
  final String? currentLocation;

  @override
  String toString() {
    return 'CreateDriverModel(name: $name, phone: $phone, zalo: $zalo, status: $status, currentLocation: $currentLocation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateDriverModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.zalo, zalo) || other.zalo == zalo) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, phone, zalo, status, currentLocation);

  /// Create a copy of CreateDriverModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateDriverModelImplCopyWith<_$CreateDriverModelImpl> get copyWith =>
      __$$CreateDriverModelImplCopyWithImpl<_$CreateDriverModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateDriverModelImplToJson(this);
  }
}

abstract class _CreateDriverModel implements CreateDriverModel {
  const factory _CreateDriverModel({
    required final String name,
    required final String phone,
    final String? zalo,
    final DriverStatus status,
    final String? currentLocation,
  }) = _$CreateDriverModelImpl;

  factory _CreateDriverModel.fromJson(Map<String, dynamic> json) =
      _$CreateDriverModelImpl.fromJson;

  @override
  String get name;
  @override
  String get phone;
  @override
  String? get zalo;
  @override
  DriverStatus get status;
  @override
  String? get currentLocation;

  /// Create a copy of CreateDriverModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateDriverModelImplCopyWith<_$CreateDriverModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
