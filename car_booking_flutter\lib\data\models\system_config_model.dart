import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/system_config.dart';

part 'system_config_model.freezed.dart';
part 'system_config_model.g.dart';

@freezed
class SystemConfigModel with _$SystemConfigModel {
  const factory SystemConfigModel({
    required String id,
    @Default(10) int defaultCommissionRate,
    @Default(70) int driverSharePercentage,
    @Default(25000) int cancelPenalty,
    @Default(50000) int noPickupPenalty,
    @Default(75000) int missedPickupPenalty,
    @Default(15) int warningTimeMinutes,
    DateTime? updatedAt,
  }) = _SystemConfigModel;

  factory SystemConfigModel.fromJson(Map<String, dynamic> json) => _$SystemConfigModelFromJson(json);
}

@freezed
class UpdateSystemConfigModel with _$UpdateSystemConfigModel {
  const factory UpdateSystemConfigModel({
    int? defaultCommissionRate,
    int? driverSharePercentage,
    int? cancelPenalty,
    int? noPickupPenalty,
    int? missedPickupPenalty,
    int? warningTimeMinutes,
  }) = _UpdateSystemConfigModel;

  factory UpdateSystemConfigModel.fromJson(Map<String, dynamic> json) => _$UpdateSystemConfigModelFromJson(json);
}

// Extensions to convert between models and entities
extension SystemConfigModelX on SystemConfigModel {
  SystemConfig toEntity() {
    return SystemConfig(
      id: id,
      defaultCommissionRate: defaultCommissionRate,
      driverSharePercentage: driverSharePercentage,
      cancelPenalty: cancelPenalty,
      noPickupPenalty: noPickupPenalty,
      missedPickupPenalty: missedPickupPenalty,
      warningTimeMinutes: warningTimeMinutes,
      updatedAt: updatedAt,
    );
  }
}

extension UpdateSystemConfigModelX on UpdateSystemConfigModel {
  UpdateSystemConfig toEntity() {
    return UpdateSystemConfig(
      defaultCommissionRate: defaultCommissionRate,
      driverSharePercentage: driverSharePercentage,
      cancelPenalty: cancelPenalty,
      noPickupPenalty: noPickupPenalty,
      missedPickupPenalty: missedPickupPenalty,
      warningTimeMinutes: warningTimeMinutes,
    );
  }
}
