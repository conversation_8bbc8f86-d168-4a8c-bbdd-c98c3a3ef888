import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

enum StatusBadgeVariant {
  green,
  yellow,
  blue,
  red,
  gray,
}

class StatusBadge extends StatelessWidget {
  const StatusBadge({
    super.key,
    required this.status,
    this.variant = StatusBadgeVariant.gray,
    this.size = StatusBadgeSize.medium,
  });

  final String status;
  final StatusBadgeVariant variant;
  final StatusBadgeSize size;

  Color get _backgroundColor {
    switch (variant) {
      case StatusBadgeVariant.green:
        return AppColors.successLight;
      case StatusBadgeVariant.yellow:
        return AppColors.warningLight;
      case StatusBadgeVariant.blue:
        return AppColors.infoLight;
      case StatusBadgeVariant.red:
        return AppColors.errorLight;
      case StatusBadgeVariant.gray:
        return AppColors.gray100;
    }
  }

  Color get _textColor {
    switch (variant) {
      case StatusBadgeVariant.green:
        return AppColors.success;
      case StatusBadgeVariant.yellow:
        return const Color(0xFFD97706); // Darker yellow for better contrast
      case StatusBadgeVariant.blue:
        return AppColors.info;
      case StatusBadgeVariant.red:
        return AppColors.error;
      case StatusBadgeVariant.gray:
        return AppColors.gray700;
    }
  }

  EdgeInsets get _padding {
    switch (size) {
      case StatusBadgeSize.small:
        return const EdgeInsets.symmetric(horizontal: 6, vertical: 2);
      case StatusBadgeSize.medium:
        return const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
      case StatusBadgeSize.large:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
    }
  }

  TextStyle get _textStyle {
    switch (size) {
      case StatusBadgeSize.small:
        return AppTextStyles.labelSmall.copyWith(color: _textColor);
      case StatusBadgeSize.medium:
        return AppTextStyles.labelMedium.copyWith(color: _textColor);
      case StatusBadgeSize.large:
        return AppTextStyles.labelLarge.copyWith(color: _textColor);
    }
  }

  double get _borderRadius {
    switch (size) {
      case StatusBadgeSize.small:
        return 12;
      case StatusBadgeSize.medium:
        return 16;
      case StatusBadgeSize.large:
        return 20;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: _padding,
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: Text(
        status,
        style: _textStyle,
      ),
    );
  }
}

enum StatusBadgeSize {
  small,
  medium,
  large,
}
