import 'package:equatable/equatable.dart';
import '../../../domain/repositories/ride_repository.dart';

abstract class AvailableRidesEvent extends Equatable {
  const AvailableRidesEvent();

  @override
  List<Object?> get props => [];
}

class LoadAvailableRides extends AvailableRidesEvent {
  const LoadAvailableRides({this.location});

  final LocationFilter? location;

  @override
  List<Object?> get props => [location];
}

class RefreshAvailableRides extends AvailableRidesEvent {
  const RefreshAvailableRides({this.location});

  final LocationFilter? location;

  @override
  List<Object?> get props => [location];
}

class AcceptRide extends AvailableRidesEvent {
  const AcceptRide({
    required this.rideId,
    required this.driverId,
  });

  final String rideId;
  final String driverId;

  @override
  List<Object?> get props => [rideId, driverId];
}

class FilterRidesByLocation extends AvailableRidesEvent {
  const FilterRidesByLocation(this.location);

  final LocationFilter? location;

  @override
  List<Object?> get props => [location];
}
