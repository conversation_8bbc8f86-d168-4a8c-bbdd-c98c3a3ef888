import { useQuery } from "@tanstack/react-query";
import { TrendingUp, DollarSign, Car, Percent } from "lucide-react";

export default function Reports() {
  const { data: stats } = useQuery({
    queryKey: ['/api/dashboard/stats'],
  });

  const formatCurrency = (amount: number) => {
    return (amount / 1000000).toFixed(1) + 'M VNĐ';
  };

  const mockMonthlyStats = [
    { label: 'Tổng cuốc xe tháng này', value: '1,247', change: '+12%', trend: 'up' },
    { label: 'Doanh thu tháng này', value: '456M', change: '+8%', trend: 'up' },
    { label: 'Hoa hồng thu được', value: '52M', change: '+15%', trend: 'up' },
  ];

  return (
    <div className="space-y-6">
      {/* Revenue Chart Placeholder */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Thống kê doanh thu</h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <TrendingUp className="w-16 h-16 mx-auto mb-2 opacity-50" />
              <p className="text-lg font-medium">Biểu đồ doanh thu</p>
              <p className="text-sm">Tích hợp Chart.js hoặc thư viện tương tự</p>
              <p className="text-sm mt-2">
                Hiển thị xu hướng doanh thu theo ngày/tuần/tháng
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockMonthlyStats.map((stat, index) => (
          <div key={index} className="bg-white shadow rounded-lg p-6">
            <h4 className="text-sm font-medium text-gray-500 mb-2">{stat.label}</h4>
            <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
            <p className={`text-sm mt-2 ${
              stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              <TrendingUp className="w-4 h-4 mr-1 inline" />
              {stat.change} so với tháng trước
            </p>
          </div>
        ))}
      </div>

      {/* Additional Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Drivers */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tài xế xuất sắc</h3>
            <div className="space-y-3">
              {[
                { name: 'Nguyễn Văn A', rides: '156 cuốc', revenue: '45.2M' },
                { name: 'Trần Văn B', rides: '142 cuốc', revenue: '38.7M' },
                { name: 'Lê Thị C', rides: '138 cuốc', revenue: '35.1M' },
              ].map((driver, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{driver.name}</p>
                    <p className="text-sm text-gray-500">{driver.rides}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-primary">{driver.revenue}</p>
                    <p className="text-sm text-gray-500">Doanh thu</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Popular Routes */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tuyến đường phổ biến</h3>
            <div className="space-y-3">
              {[
                { route: 'Quận 1 → Quận 7', count: '89 cuốc', percentage: '15.2%' },
                { route: 'Quận 3 → Quận 1', count: '67 cuốc', percentage: '11.4%' },
                { route: 'Quận 1 → Tân Bình', count: '52 cuốc', percentage: '8.9%' },
              ].map((route, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{route.route}</p>
                    <p className="text-sm text-gray-500">{route.count}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-blue-600">{route.percentage}</p>
                    <p className="text-sm text-gray-500">Tổng số</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
