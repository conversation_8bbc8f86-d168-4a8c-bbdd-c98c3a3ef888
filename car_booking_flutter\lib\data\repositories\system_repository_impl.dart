import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../../domain/entities/system_config.dart';
import '../../domain/entities/dashboard_stats.dart';
import '../../domain/entities/penalty.dart';
import '../../domain/repositories/system_repository.dart';
import '../datasources/system_remote_data_source.dart';
import '../datasources/local_storage_service.dart';
import '../models/system_config_model.dart';
import '../models/penalty_model.dart';

class SystemRepositoryImpl implements SystemRepository {
  final SystemRemoteDataSource _remoteDataSource;
  final LocalStorageService _localStorage;

  SystemRepositoryImpl(this._remoteDataSource, this._localStorage);

  @override
  Future<Either<Failure, SystemConfig>> getSystemConfig() async {
    try {
      // Try to get from cache first
      const cacheKey = 'system_config';
      const maxAge = Duration(minutes: 10);
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final configModel = SystemConfigModel.fromJson(cachedData['config'] as Map<String, dynamic>);
          return Right(configModel.toEntity());
        }
      }

      // Fetch from remote
      final remoteConfig = await _remoteDataSource.getSystemConfig();
      final config = remoteConfig.toEntity();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'config': remoteConfig.toJson(),
      });

      return Right(config);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, SystemConfig>> updateSystemConfig(UpdateSystemConfig config) async {
    try {
      final updateConfigModel = UpdateSystemConfigModel(
        defaultCommissionRate: config.defaultCommissionRate,
        driverSharePercentage: config.driverSharePercentage,
        cancelPenalty: config.cancelPenalty,
        noPickupPenalty: config.noPickupPenalty,
        missedPickupPenalty: config.missedPickupPenalty,
        warningTimeMinutes: config.warningTimeMinutes,
      );
      
      final remoteConfig = await _remoteDataSource.updateSystemConfig(updateConfigModel);
      
      // Invalidate config cache
      await _localStorage.removeCachedData('system_config');
      
      return Right(remoteConfig.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, DashboardStats>> getDashboardStats() async {
    try {
      // Try to get from cache first
      const cacheKey = 'dashboard_stats';
      const maxAge = Duration(minutes: 1); // Short cache for stats
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final statsModel = DashboardStatsModel.fromJson(cachedData['stats'] as Map<String, dynamic>);
          return Right(statsModel.toEntity());
        }
      }

      // Fetch from remote
      final remoteStats = await _remoteDataSource.getDashboardStats();
      final stats = remoteStats.toEntity();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'stats': remoteStats.toJson(),
      });

      return Right(stats);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Penalty>>> getDriverPenalties(String driverId) async {
    try {
      final cacheKey = 'driver_penalties_$driverId';
      const maxAge = Duration(minutes: 5);
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final List<dynamic> penaltiesJson = cachedData['penalties'] as List<dynamic>;
          final penalties = penaltiesJson
              .map((json) => PenaltyModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();
          return Right(penalties);
        }
      }

      // Fetch from remote
      final remotePenalties = await _remoteDataSource.getDriverPenalties(driverId);
      final penalties = remotePenalties.map((model) => model.toEntity()).toList();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'penalties': remotePenalties.map((model) => model.toJson()).toList(),
      });

      return Right(penalties);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Penalty>> createPenalty(CreatePenalty penalty) async {
    try {
      final createPenaltyModel = CreatePenaltyModel(
        driverId: penalty.driverId,
        rideId: penalty.rideId,
        type: penalty.type,
        amount: penalty.amount,
        reason: penalty.reason,
      );
      
      final remotePenalty = await _remoteDataSource.createPenalty(createPenaltyModel);
      
      // Invalidate penalties cache for this driver
      await _localStorage.removeCachedData('driver_penalties_${penalty.driverId}');
      
      return Right(remotePenalty.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }
}
