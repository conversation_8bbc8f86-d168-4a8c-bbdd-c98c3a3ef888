import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/driver.dart';
import '../../../domain/usecases/get_all_drivers.dart';

// States
abstract class DriverManagementState extends Equatable {
  const DriverManagementState();

  @override
  List<Object?> get props => [];
}

class DriverManagementInitial extends DriverManagementState {}

class DriverManagementLoading extends DriverManagementState {}

class DriverManagementLoaded extends DriverManagementState {
  const DriverManagementLoaded(this.drivers);

  final List<DriverWithStats> drivers;

  @override
  List<Object?> get props => [drivers];
}

class DriverManagementError extends DriverManagementState {
  const DriverManagementError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

// Cubit
class DriverManagementCubit extends Cubit<DriverManagementState> {
  final GetAllDriversUseCase _getAllDrivers;

  DriverManagementCubit({
    required GetAllDriversUseCase getAllDrivers,
  })  : _getAllDrivers = getAllDrivers,
        super(DriverManagementInitial());

  Future<void> loadDrivers() async {
    emit(DriverManagementLoading());
    
    final result = await _getAllDrivers();
    
    result.fold(
      (failure) => emit(DriverManagementError(failure.message)),
      (drivers) => emit(DriverManagementLoaded(drivers)),
    );
  }

  Future<void> refreshDrivers() async {
    // Don't show loading state for refresh
    final result = await _getAllDrivers();
    
    result.fold(
      (failure) => emit(DriverManagementError(failure.message)),
      (drivers) => emit(DriverManagementLoaded(drivers)),
    );
  }
}
