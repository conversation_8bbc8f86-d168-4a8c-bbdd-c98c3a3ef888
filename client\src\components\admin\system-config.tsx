import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { insertSystemConfigSchema } from "@shared/schema";
import { z } from "zod";

const configSchema = insertSystemConfigSchema.partial();
type ConfigForm = z.infer<typeof configSchema>;

export default function SystemConfig() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: config, isLoading } = useQuery({
    queryKey: ['/api/config'],
  });

  const form = useForm<ConfigForm>({
    resolver: zodResolver(configSchema),
    defaultValues: config || {},
  });

  // Update form when config data loads
  if (config && !form.formState.isDirty) {
    form.reset(config);
  }

  const updateConfigMutation = useMutation({
    mutationFn: async (data: ConfigForm) => {
      return apiRequest('PATCH', '/api/config', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/config'] });
      toast({
        title: "Thành công",
        description: "Đã cập nhật cấu hình hệ thống!",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật cấu hình. Vui lòng thử lại.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ConfigForm) => {
    updateConfigMutation.mutate(data);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-4">
                {[1, 2].map((i) => (
                  <div key={i} className="h-10 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Commission Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cấu hình hoa hồng</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">
                Hoa hồng mặc định (%)
              </Label>
              <Input
                type="number"
                min="0"
                max="50"
                {...form.register('defaultCommissionRate', { valueAsNumber: true })}
                className="w-full"
              />
            </div>
            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">
                Phí chia sẻ tài xế nhận (%)
              </Label>
              <Input
                type="number"
                min="0"
                max="100"
                {...form.register('driverSharePercentage', { valueAsNumber: true })}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Penalty Settings */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cấu hình phí phạt</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">
                  Hủy cuốc do sai sót (VNĐ)
                </Label>
                <Input
                  type="number"
                  min="0"
                  {...form.register('cancelPenalty', { valueAsNumber: true })}
                  className="w-full"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">
                  Không đón khách (VNĐ)
                </Label>
                <Input
                  type="number"
                  min="0"
                  {...form.register('noPickupPenalty', { valueAsNumber: true })}
                  className="w-full"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">
                  Ngủ quên, quên đón khách (VNĐ)
                </Label>
                <Input
                  type="number"
                  min="0"
                  {...form.register('missedPickupPenalty', { valueAsNumber: true })}
                  className="w-full"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">
                  Thời gian cảnh báo (phút)
                </Label>
                <Input
                  type="number"
                  min="5"
                  max="60"
                  {...form.register('warningTimeMinutes', { valueAsNumber: true })}
                  className="w-full"
                />
              </div>
            </div>
          </div>
          
          <Button
            type="submit"
            disabled={updateConfigMutation.isPending}
            className="mt-4 bg-primary hover:bg-green-700 text-white"
          >
            <Save className="w-4 h-4 mr-2" />
            {updateConfigMutation.isPending ? 'Đang lưu...' : 'Lưu cấu hình'}
          </Button>
        </div>
      </div>
    </form>
  );
}
