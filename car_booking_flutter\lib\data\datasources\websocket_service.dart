import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../../core/constants/api_constants.dart';
import '../../core/errors/exceptions.dart';

enum WebSocketEventType {
  rideCreated('ride_created'),
  rideAccepted('ride_accepted'),
  rideStatusUpdated('ride_status_updated'),
  driverCreated('driver_created'),
  driverStatusUpdated('driver_status_updated');

  const WebSocketEventType(this.value);
  final String value;

  static WebSocketEventType? fromString(String value) {
    for (final type in WebSocketEventType.values) {
      if (type.value == value) return type;
    }
    return null;
  }
}

class WebSocketMessage {
  const WebSocketMessage({
    required this.type,
    required this.data,
  });

  final WebSocketEventType type;
  final Map<String, dynamic> data;

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    final type = WebSocketEventType.fromString(json['type'] as String);
    if (type == null) {
      throw const ValidationException('Unknown WebSocket message type');
    }
    
    return WebSocketMessage(
      type: type,
      data: json['data'] as Map<String, dynamic>,
    );
  }
}

class WebSocketService {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  final StreamController<WebSocketMessage> _messageController = 
      StreamController<WebSocketMessage>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();
  
  Timer? _reconnectTimer;
  bool _isConnecting = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 3);

  Stream<WebSocketMessage> get messageStream => _messageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;

  bool get isConnected => _channel != null;

  Future<void> connect() async {
    if (_isConnecting || isConnected) return;
    
    _isConnecting = true;
    _shouldReconnect = true;
    
    try {
      print('[WebSocket] Connecting to ${ApiConstants.wsUrl}');
      _channel = WebSocketChannel.connect(Uri.parse(ApiConstants.wsUrl));
      
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );
      
      _connectionController.add(true);
      _reconnectAttempts = 0;
      _isConnecting = false;
      
      print('[WebSocket] Connected successfully');
    } catch (e) {
      _isConnecting = false;
      _connectionController.add(false);
      print('[WebSocket] Connection failed: $e');
      
      if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
        _scheduleReconnect();
      }
    }
  }

  void _onMessage(dynamic message) {
    try {
      final json = jsonDecode(message as String) as Map<String, dynamic>;
      final wsMessage = WebSocketMessage.fromJson(json);
      _messageController.add(wsMessage);
      print('[WebSocket] Received message: ${wsMessage.type.value}');
    } catch (e) {
      print('[WebSocket] Failed to parse message: $e');
    }
  }

  void _onError(error) {
    print('[WebSocket] Error: $error');
    _connectionController.add(false);
    
    if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  void _onDisconnected() {
    print('[WebSocket] Disconnected');
    _channel = null;
    _subscription = null;
    _connectionController.add(false);
    
    if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  void _scheduleReconnect() {
    _reconnectAttempts++;
    print('[WebSocket] Scheduling reconnect attempt $_reconnectAttempts/$maxReconnectAttempts');
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  Future<void> disconnect() async {
    _shouldReconnect = false;
    _reconnectTimer?.cancel();
    
    await _subscription?.cancel();
    await _channel?.sink.close(status.normalClosure);
    
    _channel = null;
    _subscription = null;
    _connectionController.add(false);
    
    print('[WebSocket] Disconnected manually');
  }

  void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }
}
