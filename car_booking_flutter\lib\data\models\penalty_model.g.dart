// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'penalty_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PenaltyModelImpl _$$PenaltyModelImplFromJson(Map<String, dynamic> json) =>
    _$PenaltyModelImpl(
      id: json['id'] as String,
      driverId: json['driverId'] as String,
      rideId: json['rideId'] as String?,
      type: $enumDecode(_$PenaltyTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toInt(),
      reason: json['reason'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$PenaltyModelImplToJson(_$PenaltyModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'driverId': instance.driverId,
      'rideId': instance.rideId,
      'type': _$PenaltyTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'reason': instance.reason,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

const _$PenaltyTypeEnumMap = {
  PenaltyType.cancel: 'cancel',
  PenaltyType.noPickup: 'noPickup',
  PenaltyType.missedPickup: 'missedPickup',
};

_$CreatePenaltyModelImpl _$$CreatePenaltyModelImplFromJson(
  Map<String, dynamic> json,
) => _$CreatePenaltyModelImpl(
  driverId: json['driverId'] as String,
  rideId: json['rideId'] as String?,
  type: $enumDecode(_$PenaltyTypeEnumMap, json['type']),
  amount: (json['amount'] as num).toInt(),
  reason: json['reason'] as String?,
);

Map<String, dynamic> _$$CreatePenaltyModelImplToJson(
  _$CreatePenaltyModelImpl instance,
) => <String, dynamic>{
  'driverId': instance.driverId,
  'rideId': instance.rideId,
  'type': _$PenaltyTypeEnumMap[instance.type]!,
  'amount': instance.amount,
  'reason': instance.reason,
};
