import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useState } from "react";
import { Car, Settings } from "lucide-react";
import DriverDashboard from "@/pages/driver-dashboard";
import AdminDashboard from "@/pages/admin-dashboard";
import NotFound from "@/pages/not-found";

function Router() {
  const [userRole, setUserRole] = useState<'driver' | 'admin'>('driver');

  const switchRole = (role: 'driver' | 'admin') => {
    setUserRole(role);
  };

  return (
    <div className="min-h-screen">
      {/* Header Navigation */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Car className="text-primary text-2xl mr-3" />
              <h1 className="text-xl font-bold text-gray-900">Hệ Thống Đặt Xe</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button 
                  onClick={() => switchRole('driver')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    userRole === 'driver' 
                      ? 'bg-primary text-white' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Car className="w-4 h-4 mr-2 inline" />
                  Tài Xế
                </button>
                <button 
                  onClick={() => switchRole('admin')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    userRole === 'admin' 
                      ? 'bg-primary text-white' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Settings className="w-4 h-4 mr-2 inline" />
                  Admin
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <Switch>
        <Route path="/">
          {userRole === 'driver' ? <DriverDashboard /> : <AdminDashboard />}
        </Route>
        <Route component={NotFound} />
      </Switch>
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
