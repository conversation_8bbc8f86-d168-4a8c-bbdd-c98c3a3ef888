enum RideStatus {
  waiting('waiting', '<PERSON>ờ nhận'),
  accepted('accepted', 'Đã nhận'),
  inProgress('in_progress', '<PERSON><PERSON> thực hiện'),
  completed('completed', '<PERSON><PERSON><PERSON> thành'),
  cancelled('cancelled', 'Đã hủy');

  const RideStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static RideStatus fromString(String value) {
    return RideStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => RideStatus.waiting,
    );
  }
}

enum VehicleType {
  fourSeats('4_seats', '4 chỗ'),
  sevenSeats('7_seats', '7 chỗ'),
  nineSeats('9_seats', '9 chỗ'),
  sixteenSeats('16_seats', '16 chỗ');

  const VehicleType(this.value, this.displayName);

  final String value;
  final String displayName;

  static VehicleType fromString(String value) {
    return VehicleType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => VehicleType.fourSeats,
    );
  }
}

enum DriverStatus {
  active('active', '<PERSON>ạt động'),
  inactive('inactive', 'Offline'),
  busy('busy', 'Bận');

  const DriverStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static DriverStatus fromString(String value) {
    return DriverStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => DriverStatus.active,
    );
  }
}

enum PenaltyType {
  cancel('cancel', 'Hủy cuốc'),
  noPickup('no_pickup', 'Không đón khách'),
  missedPickup('missed_pickup', 'Ngủ quên, quên đón khách');

  const PenaltyType(this.value, this.displayName);

  final String value;
  final String displayName;

  static PenaltyType fromString(String value) {
    return PenaltyType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PenaltyType.cancel,
    );
  }
}
