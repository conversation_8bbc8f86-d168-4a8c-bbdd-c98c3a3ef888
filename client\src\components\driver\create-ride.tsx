import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Car, CarFront, Bus, Truck, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { insertRideSchema } from "@shared/schema";
import { z } from "zod";

const createRideSchema = insertRideSchema.extend({
  pickupTime: z.string().min(1, "Vui lòng chọn thời gian đón"),
  price: z.number().min(50000, "<PERSON><PERSON><PERSON> tối thiểu 50,000 VNĐ"),
  commissionRate: z.number().min(1, "Hoa hồng tối thiểu 1%").max(50, "Hoa hồng tối đa 50%"),
  pickupAddress: z.string().min(10, "Địa chỉ quá ngắn"),
  passengerZalo: z.string().min(10, "Số Zalo không hợp lệ"),
});

type CreateRideForm = z.infer<typeof createRideSchema>;

export default function CreateRide() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedVehicle, setSelectedVehicle] = useState<string>('');
  const currentDriverId = 'demo-driver-id'; // In real app, get from auth context

  const form = useForm<CreateRideForm>({
    resolver: zodResolver(createRideSchema),
    defaultValues: {
      creatorId: currentDriverId,
      vehicleType: '4_seats' as any,
      price: 0,
      commissionRate: 10,
      pickupTime: new Date().toISOString().slice(0, 16),
      pickupAddress: '',
      passengerZalo: '',
      isAsap: false,
      province: 'TP.HCM',
      district: 'Quận 1',
      commune: 'Bến Nghé',
    },
  });

  const createRideMutation = useMutation({
    mutationFn: async (data: CreateRideForm) => {
      const rideData = {
        ...data,
        pickupTime: new Date(data.pickupTime).toISOString(),
      };
      return apiRequest('POST', '/api/rides', rideData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/rides/available'] });
      form.reset();
      setSelectedVehicle('');
      toast({
        title: "Thành công",
        description: "Đã tạo cuốc xe thành công!",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể tạo cuốc xe. Vui lòng thử lại.",
        variant: "destructive",
      });
    },
  });

  const vehicleTypes = [
    { value: '4_seats', label: '4 chỗ', icon: Car },
    { value: '7_seats', label: '7 chỗ', icon: CarFront },
    { value: '9_seats', label: '9 chỗ', icon: Truck },
    { value: '16_seats', label: '16 chỗ', icon: Bus },
  ];

  const onSubmit = (data: CreateRideForm) => {
    createRideMutation.mutate(data);
  };

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold mb-4">Tạo Cuốc Xe Mới</h2>
      
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Vehicle Type */}
        <div>
          <Label className="block text-sm font-medium text-gray-700 mb-2">Loại xe</Label>
          <div className="grid grid-cols-2 gap-2">
            {vehicleTypes.map((vehicle) => {
              const IconComponent = vehicle.icon;
              const isSelected = selectedVehicle === vehicle.value;
              
              return (
                <button
                  key={vehicle.value}
                  type="button"
                  onClick={() => {
                    setSelectedVehicle(vehicle.value);
                    form.setValue('vehicleType', vehicle.value as any);
                  }}
                  className={`p-3 border-2 rounded-lg text-center transition-colors ${
                    isSelected
                      ? 'border-primary bg-blue-50'
                      : 'border-gray-200 hover:border-primary'
                  }`}
                >
                  <IconComponent className="w-6 h-6 mx-auto mb-1" />
                  <span className="text-sm font-medium">{vehicle.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Pickup Time */}
        <div>
          <Label className="block text-sm font-medium text-gray-700 mb-2">Thời gian đón khách</Label>
          <div className="space-y-2">
            <Input
              type="datetime-local"
              {...form.register('pickupTime')}
              className="text-base p-3"
            />
            <div className="flex items-center space-x-2">
              <Checkbox
                {...form.register('isAsap')}
                id="asap"
              />
              <Label htmlFor="asap" className="text-sm">
                Đi ngay càng sớm càng tốt (10-20 phút)
              </Label>
            </div>
          </div>
        </div>

        {/* Price and Commission */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="block text-sm font-medium text-gray-700 mb-2">Giá cuốc xe (VNĐ)</Label>
            <Input
              type="number"
              placeholder="650000"
              {...form.register('price', { valueAsNumber: true })}
              className="p-3 text-base"
            />
          </div>
          <div>
            <Label className="block text-sm font-medium text-gray-700 mb-2">Hoa hồng (%)</Label>
            <Input
              type="number"
              placeholder="10"
              min="1"
              max="50"
              {...form.register('commissionRate', { valueAsNumber: true })}
              className="p-3 text-base"
            />
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <Label className="block text-sm font-medium text-gray-700 mb-2">Địa chỉ đón khách</Label>
          <Textarea
            placeholder="123 Lê Lợi, Phường Bến Nghé, Quận 1, TP.HCM"
            rows={3}
            {...form.register('pickupAddress')}
            className="p-3 text-base resize-none"
          />
        </div>

        <div>
          <Label className="block text-sm font-medium text-gray-700 mb-2">Zalo người cần đón</Label>
          <Input
            type="tel"
            placeholder="0907654321"
            {...form.register('passengerZalo')}
            className="p-3 text-base"
          />
        </div>

        <Button
          type="submit"
          disabled={createRideMutation.isPending}
          className="w-full bg-primary hover:bg-green-700 text-white py-4 rounded-lg font-semibold text-lg"
        >
          <Plus className="w-5 h-5 mr-2" />
          {createRideMutation.isPending ? 'Đang tạo...' : 'Tạo Cuốc Xe'}
        </Button>
      </form>
    </div>
  );
}
