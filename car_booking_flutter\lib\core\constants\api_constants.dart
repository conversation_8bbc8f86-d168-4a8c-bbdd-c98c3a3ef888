class ApiConstants {
  // Base URL - should be configurable for different environments
  static const String baseUrl = 'http://localhost:5000';
  
  // WebSocket URL
  static const String wsUrl = 'ws://localhost:5000/ws';
  
  // API Endpoints
  static const String drivers = '/api/drivers';
  static const String rides = '/api/rides';
  static const String ridesAvailable = '/api/rides/available';
  static const String config = '/api/config';
  static const String dashboardStats = '/api/dashboard/stats';
  
  // Dynamic endpoints
  static String rideHistory(String driverId) => '/api/rides/history/$driverId';
  static String acceptRide(String rideId) => '/api/rides/$rideId/accept';
  static String updateRideStatus(String rideId) => '/api/rides/$rideId/status';
  static String driverPenalties(String driverId) => '/api/drivers/$driverId/penalties';
  
  // Request timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
}
