import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../../data/datasources/websocket_service.dart';
import '../../../data/datasources/local_storage_service.dart';

// States
abstract class AppState extends Equatable {
  const AppState();

  @override
  List<Object?> get props => [];
}

class AppInitial extends AppState {}

class AppLoading extends AppState {}

class AppReady extends AppState {
  const AppReady({
    required this.userRole,
    required this.currentDriverId,
    required this.isOnline,
    required this.isWebSocketConnected,
  });

  final String userRole;
  final String? currentDriverId;
  final bool isOnline;
  final bool isWebSocketConnected;

  @override
  List<Object?> get props => [userRole, currentDriverId, isOnline, isWebSocketConnected];

  AppReady copyWith({
    String? userRole,
    String? currentDriverId,
    bool? isOnline,
    bool? isWebSocketConnected,
  }) {
    return AppReady(
      userRole: userRole ?? this.userRole,
      currentDriverId: currentDriverId ?? this.currentDriverId,
      isOnline: isOnline ?? this.isOnline,
      isWebSocketConnected: isWebSocketConnected ?? this.isWebSocketConnected,
    );
  }
}

class AppError extends AppState {
  const AppError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

// Cubit
class AppCubit extends Cubit<AppState> {
  final WebSocketService _webSocketService;
  final LocalStorageService _localStorage;
  final Connectivity _connectivity;
  
  StreamSubscription? _connectivitySubscription;
  StreamSubscription? _webSocketConnectionSubscription;
  Timer? _autoRefreshTimer;

  AppCubit({
    required WebSocketService webSocketService,
    required LocalStorageService localStorage,
    required Connectivity connectivity,
  })  : _webSocketService = webSocketService,
        _localStorage = localStorage,
        _connectivity = connectivity,
        super(AppInitial());

  Future<void> initialize() async {
    try {
      emit(AppLoading());

      // Get saved user preferences
      final userRole = _localStorage.getUserRole() ?? 'driver';
      final currentDriverId = _localStorage.getCurrentDriverId();

      // Check initial connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      // Initialize WebSocket connection
      if (isOnline) {
        await _webSocketService.connect();
      }

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen((result) {
        final isOnline = result != ConnectivityResult.none;
        _onConnectivityChanged(isOnline);
      });

      // Listen to WebSocket connection changes
      _webSocketConnectionSubscription = _webSocketService.connectionStream.listen((isConnected) {
        _onWebSocketConnectionChanged(isConnected);
      });

      emit(AppReady(
        userRole: userRole,
        currentDriverId: currentDriverId,
        isOnline: isOnline,
        isWebSocketConnected: _webSocketService.isConnected,
      ));

      // Start auto-refresh timer for real-time updates
      _startAutoRefresh();

    } catch (e) {
      emit(AppError('Failed to initialize app: $e'));
    }
  }

  Future<void> switchUserRole(String role) async {
    try {
      await _localStorage.setUserRole(role);
      
      if (state is AppReady) {
        final currentState = state as AppReady;
        emit(currentState.copyWith(userRole: role));
      }
    } catch (e) {
      emit(AppError('Failed to switch user role: $e'));
    }
  }

  Future<void> setCurrentDriverId(String driverId) async {
    try {
      await _localStorage.setCurrentDriverId(driverId);
      
      if (state is AppReady) {
        final currentState = state as AppReady;
        emit(currentState.copyWith(currentDriverId: driverId));
      }
    } catch (e) {
      emit(AppError('Failed to set current driver ID: $e'));
    }
  }

  void _onConnectivityChanged(bool isOnline) {
    if (state is AppReady) {
      final currentState = state as AppReady;
      emit(currentState.copyWith(isOnline: isOnline));

      if (isOnline && !_webSocketService.isConnected) {
        // Reconnect WebSocket when coming back online
        _webSocketService.connect();
      } else if (!isOnline) {
        // Disconnect WebSocket when going offline
        _webSocketService.disconnect();
      }
    }
  }

  void _onWebSocketConnectionChanged(bool isConnected) {
    if (state is AppReady) {
      final currentState = state as AppReady;
      emit(currentState.copyWith(isWebSocketConnected: isConnected));
    }
  }

  void _startAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      // This timer can be used to trigger periodic updates in other blocs
      // For now, it's just a placeholder for future auto-refresh functionality
    });
  }

  Future<void> reconnectWebSocket() async {
    try {
      await _webSocketService.connect();
    } catch (e) {
      emit(AppError('Failed to reconnect WebSocket: $e'));
    }
  }

  @override
  Future<void> close() {
    _connectivitySubscription?.cancel();
    _webSocketConnectionSubscription?.cancel();
    _autoRefreshTimer?.cancel();
    _webSocketService.dispose();
    return super.close();
  }
}
