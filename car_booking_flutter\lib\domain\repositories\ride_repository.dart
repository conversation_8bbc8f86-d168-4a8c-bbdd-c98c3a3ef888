import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../entities/ride.dart';

class LocationFilter {
  const LocationFilter({
    this.province,
    this.district,
    this.commune,
  });

  final String? province;
  final String? district;
  final String? commune;
}

abstract class RideRepository {
  Future<Either<Failure, List<RideWithDrivers>>> getAllRides();
  Future<Either<Failure, List<RideWithDrivers>>> getAvailableRides({LocationFilter? location});
  Future<Either<Failure, List<RideWithDrivers>>> getRideHistory(String driverId);
  Future<Either<Failure, RideWithDrivers>> getRide(String id);
  Future<Either<Failure, RideWithDrivers>> createRide(CreateRide ride);
  Future<Either<Failure, void>> acceptRide(String rideId, String driverId);
  Future<Either<Failure, void>> updateRideStatus(String rideId, String status);
}
