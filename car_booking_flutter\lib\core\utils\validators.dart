class Validators {
  // Phone number validation for Vietnamese numbers
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Số điện thoại không được để trống';
    }

    // Remove all non-digit characters
    final digits = value.replaceAll(RegExp(r'\D'), '');

    // Check for Vietnamese phone number patterns
    if (digits.length == 10 && digits.startsWith('0')) {
      // Mobile numbers: 09x, 08x, 07x, 05x, 03x
      final mobilePatterns = ['09', '08', '07', '05', '03'];
      if (mobilePatterns.any((pattern) => digits.startsWith(pattern))) {
        return null; // Valid
      }
    } else if (digits.length == 11 && digits.startsWith('84')) {
      // International format: +84
      final withoutCountryCode = digits.substring(2);
      final mobilePatterns = ['9', '8', '7', '5', '3'];
      if (mobilePatterns.any((pattern) => withoutCountryCode.startsWith(pattern))) {
        return null; // Valid
      }
    }

    return 'S<PERSON> điện thoại không hợp lệ';
  }

  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Tên không được để trống';
    }

    if (value.trim().length < 2) {
      return 'Tên phải có ít nhất 2 ký tự';
    }

    if (value.trim().length > 50) {
      return 'Tên không được quá 50 ký tự';
    }

    // Check for valid characters (Vietnamese characters, spaces, and basic punctuation)
    final nameRegex = RegExp(r'^[a-zA-ZÀ-ỹ\s\-\.]+$');
    if (!nameRegex.hasMatch(value.trim())) {
      return 'Tên chỉ được chứa chữ cái và khoảng trắng';
    }

    return null;
  }

  // Price validation
  static String? validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Giá không được để trống';
    }

    final price = int.tryParse(value.replaceAll(RegExp(r'[^\d]'), ''));
    if (price == null) {
      return 'Giá phải là số';
    }

    if (price < 10000) {
      return 'Giá phải ít nhất 10,000 VNĐ';
    }

    if (price > 10000000) {
      return 'Giá không được quá 10,000,000 VNĐ';
    }

    return null;
  }

  // Commission rate validation
  static String? validateCommissionRate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Tỷ lệ hoa hồng không được để trống';
    }

    final rate = int.tryParse(value);
    if (rate == null) {
      return 'Tỷ lệ hoa hồng phải là số';
    }

    if (rate < 0) {
      return 'Tỷ lệ hoa hồng không được âm';
    }

    if (rate > 50) {
      return 'Tỷ lệ hoa hồng không được quá 50%';
    }

    return null;
  }

  // Address validation
  static String? validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Địa chỉ không được để trống';
    }

    if (value.trim().length < 10) {
      return 'Địa chỉ phải có ít nhất 10 ký tự';
    }

    if (value.trim().length > 200) {
      return 'Địa chỉ không được quá 200 ký tự';
    }

    return null;
  }

  // Zalo ID validation
  static String? validateZaloId(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Zalo ID is optional
    }

    if (value.trim().length < 3) {
      return 'Zalo ID phải có ít nhất 3 ký tự';
    }

    if (value.trim().length > 50) {
      return 'Zalo ID không được quá 50 ký tự';
    }

    return null;
  }

  // Penalty amount validation
  static String? validatePenaltyAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Số tiền phạt không được để trống';
    }

    final amount = int.tryParse(value.replaceAll(RegExp(r'[^\d]'), ''));
    if (amount == null) {
      return 'Số tiền phạt phải là số';
    }

    if (amount < 0) {
      return 'Số tiền phạt không được âm';
    }

    if (amount > 1000000) {
      return 'Số tiền phạt không được quá 1,000,000 VNĐ';
    }

    return null;
  }

  // Warning time validation
  static String? validateWarningTime(String? value) {
    if (value == null || value.isEmpty) {
      return 'Thời gian cảnh báo không được để trống';
    }

    final minutes = int.tryParse(value);
    if (minutes == null) {
      return 'Thời gian cảnh báo phải là số';
    }

    if (minutes < 5) {
      return 'Thời gian cảnh báo phải ít nhất 5 phút';
    }

    if (minutes > 60) {
      return 'Thời gian cảnh báo không được quá 60 phút';
    }

    return null;
  }

  // Percentage validation
  static String? validatePercentage(String? value) {
    if (value == null || value.isEmpty) {
      return 'Tỷ lệ phần trăm không được để trống';
    }

    final percentage = int.tryParse(value);
    if (percentage == null) {
      return 'Tỷ lệ phần trăm phải là số';
    }

    if (percentage < 0) {
      return 'Tỷ lệ phần trăm không được âm';
    }

    if (percentage > 100) {
      return 'Tỷ lệ phần trăm không được quá 100%';
    }

    return null;
  }

  // Generic required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName không được để trống';
    }
    return null;
  }

  // Email validation (if needed in the future)
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Email is optional
    }

    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Email không hợp lệ';
    }

    return null;
  }
}
