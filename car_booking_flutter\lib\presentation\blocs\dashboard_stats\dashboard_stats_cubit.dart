import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/dashboard_stats.dart';
import '../../../domain/usecases/get_dashboard_stats.dart';
import '../../../data/datasources/websocket_service.dart';

// States
abstract class DashboardStatsState extends Equatable {
  const DashboardStatsState();

  @override
  List<Object?> get props => [];
}

class DashboardStatsInitial extends DashboardStatsState {}

class DashboardStatsLoading extends DashboardStatsState {}

class DashboardStatsLoaded extends DashboardStatsState {
  const DashboardStatsLoaded(this.stats);

  final DashboardStats stats;

  @override
  List<Object?> get props => [stats];
}

class DashboardStatsError extends DashboardStatsState {
  const DashboardStatsError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

// Cubit
class DashboardStatsCubit extends Cubit<DashboardStatsState> {
  final GetDashboardStatsUseCase _getDashboardStats;
  final WebSocketService _webSocketService;
  
  StreamSubscription? _webSocketSubscription;
  Timer? _autoRefreshTimer;

  DashboardStatsCubit({
    required GetDashboardStatsUseCase getDashboardStats,
    required WebSocketService webSocketService,
  })  : _getDashboardStats = getDashboardStats,
        _webSocketService = webSocketService,
        super(DashboardStatsInitial()) {
    
    // Listen to WebSocket updates
    _webSocketSubscription = _webSocketService.messageStream.listen((message) {
      // Refresh stats when any ride or driver event occurs
      refreshStats();
    });
  }

  Future<void> loadStats() async {
    emit(DashboardStatsLoading());
    
    final result = await _getDashboardStats();
    
    result.fold(
      (failure) => emit(DashboardStatsError(failure.message)),
      (stats) => emit(DashboardStatsLoaded(stats)),
    );
  }

  Future<void> refreshStats() async {
    // Don't show loading state for refresh
    final result = await _getDashboardStats();
    
    result.fold(
      (failure) => emit(DashboardStatsError(failure.message)),
      (stats) => emit(DashboardStatsLoaded(stats)),
    );
  }

  void startAutoRefresh({Duration interval = const Duration(minutes: 1)}) {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(interval, (_) {
      refreshStats();
    });
  }

  void stopAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
  }

  @override
  Future<void> close() {
    _webSocketSubscription?.cancel();
    _autoRefreshTimer?.cancel();
    return super.close();
  }
}
