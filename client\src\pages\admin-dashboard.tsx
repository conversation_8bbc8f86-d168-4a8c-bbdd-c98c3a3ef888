import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Car, Users, DollarSign, Percent, List, Settings, BarChart3 } from "lucide-react";
import RideManagement from "@/components/admin/ride-management";
import DriverManagement from "@/components/admin/driver-management";
import SystemConfig from "@/components/admin/system-config";
import Reports from "@/components/admin/reports";
import RefreshIndicator from "@/components/ui/refresh-indicator";

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<'rides' | 'drivers' | 'config' | 'reports'>('rides');
  const [refreshTimer, setRefreshTimer] = useState(5);
  const [isOnline, setIsOnline] = useState(true);
  const queryClient = useQueryClient();

  const { data: stats } = useQuery({
    queryKey: ['/api/dashboard/stats'],
    refetchInterval: false,
    staleTime: 4000,
  });

  // Monitor connection status
  useEffect(() => {
    const checkOnline = () => setIsOnline(navigator.onLine);
    
    window.addEventListener('online', checkOnline);
    window.addEventListener('offline', checkOnline);
    
    return () => {
      window.removeEventListener('online', checkOnline);
      window.removeEventListener('offline', checkOnline);
    };
  }, []);

  // Auto-refresh timer for admin dashboard
  useEffect(() => {
    if (!isOnline) return;
    
    const interval = setInterval(() => {
      setRefreshTimer((prev) => {
        if (prev <= 1) {
          // Refresh dashboard stats and tab-specific data
          queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
          
          if (activeTab === 'rides') {
            queryClient.invalidateQueries({ queryKey: ['/api/rides'] });
          } else if (activeTab === 'drivers') {
            queryClient.invalidateQueries({ queryKey: ['/api/drivers'] });
          }
          
          return 5;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [queryClient, activeTab, isOnline]);

  const formatCurrency = (amount: number) => {
    return (amount / 1000000).toFixed(1) + 'M';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Dashboard Overview */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Tổng Quan Hệ Thống</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Car className="w-8 h-8 text-primary" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Cuốc xe hôm nay</h3>
                <p className="text-2xl font-semibold text-gray-900">{(stats as any)?.todayRides || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Tài xế hoạt động</h3>
                <p className="text-2xl font-semibold text-gray-900">{(stats as any)?.activeDrivers || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Doanh thu hôm nay</h3>
                <p className="text-2xl font-semibold text-gray-900">
                  {(stats as any)?.todayRevenue ? formatCurrency((stats as any).todayRevenue) : '0'}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Percent className="w-8 h-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Hoa hồng thu được</h3>
                <p className="text-2xl font-semibold text-gray-900">
                  {(stats as any)?.todayCommission ? formatCurrency((stats as any).todayCommission) : '0'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('rides')}
            className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'rides'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <List className="w-4 h-4 mr-2 inline" />
            Quản lý cuốc xe
          </button>
          <button
            onClick={() => setActiveTab('drivers')}
            className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'drivers'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <Users className="w-4 h-4 mr-2 inline" />
            Quản lý tài xế
          </button>
          <button
            onClick={() => setActiveTab('config')}
            className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'config'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <Settings className="w-4 h-4 mr-2 inline" />
            Cấu hình hệ thống
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className={`py-2 px-1 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'reports'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <BarChart3 className="w-4 h-4 mr-2 inline" />
            Báo cáo
          </button>
        </nav>
      </div>

      {/* Auto-refresh indicator */}
      <div className="mb-6 flex justify-center">
        <RefreshIndicator 
          countdown={refreshTimer}
          isOnline={isOnline}
        />
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'rides' && <RideManagement />}
        {activeTab === 'drivers' && <DriverManagement />}
        {activeTab === 'config' && <SystemConfig />}
        {activeTab === 'reports' && <Reports />}
      </div>
    </div>
  );
}
