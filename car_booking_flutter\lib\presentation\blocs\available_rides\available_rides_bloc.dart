import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/get_available_rides.dart';
import '../../../domain/usecases/accept_ride.dart';
import '../../../data/datasources/websocket_service.dart';
import 'available_rides_event.dart';
import 'available_rides_state.dart';

class AvailableRidesBloc extends Bloc<AvailableRidesEvent, AvailableRidesState> {
  final GetAvailableRides _getAvailableRides;
  final AcceptRideUseCase _acceptRide;
  final WebSocketService _webSocketService;
  
  StreamSubscription? _webSocketSubscription;

  AvailableRidesBloc({
    required GetAvailableRides getAvailableRides,
    required AcceptRideUseCase acceptRide,
    required WebSocketService webSocketService,
  })  : _getAvailableRides = getAvailableRides,
        _acceptRide = acceptRide,
        _webSocketService = webSocketService,
        super(AvailableRidesInitial()) {
    on<LoadAvailableRides>(_onLoadAvailableRides);
    on<RefreshAvailableRides>(_onRefreshAvailableRides);
    on<AcceptRide>(_onAcceptRide);
    on<FilterRidesByLocation>(_onFilterRidesByLocation);

    // Listen to WebSocket updates
    _webSocketSubscription = _webSocketService.messageStream.listen((message) {
      if (message.type == WebSocketEventType.rideCreated ||
          message.type == WebSocketEventType.rideAccepted ||
          message.type == WebSocketEventType.rideStatusUpdated) {
        // Refresh available rides when ride-related events occur
        add(const RefreshAvailableRides());
      }
    });
  }

  Future<void> _onLoadAvailableRides(
    LoadAvailableRides event,
    Emitter<AvailableRidesState> emit,
  ) async {
    emit(AvailableRidesLoading());
    
    final result = await _getAvailableRides(location: event.location);
    
    result.fold(
      (failure) => emit(AvailableRidesError(failure.message)),
      (rides) => emit(AvailableRidesLoaded(rides)),
    );
  }

  Future<void> _onRefreshAvailableRides(
    RefreshAvailableRides event,
    Emitter<AvailableRidesState> emit,
  ) async {
    // Don't show loading state for refresh, keep current state
    final result = await _getAvailableRides(location: event.location);
    
    result.fold(
      (failure) => emit(AvailableRidesError(failure.message)),
      (rides) => emit(AvailableRidesLoaded(rides)),
    );
  }

  Future<void> _onAcceptRide(
    AcceptRide event,
    Emitter<AvailableRidesState> emit,
  ) async {
    emit(AvailableRidesAccepting(event.rideId));
    
    final result = await _acceptRide(event.rideId, event.driverId);
    
    result.fold(
      (failure) => emit(AvailableRidesAcceptError(failure.message)),
      (_) {
        emit(AvailableRidesAcceptSuccess(event.rideId));
        // Refresh the list after successful accept
        add(const RefreshAvailableRides());
      },
    );
  }

  Future<void> _onFilterRidesByLocation(
    FilterRidesByLocation event,
    Emitter<AvailableRidesState> emit,
  ) async {
    emit(AvailableRidesLoading());
    
    final result = await _getAvailableRides(location: event.location);
    
    result.fold(
      (failure) => emit(AvailableRidesError(failure.message)),
      (rides) => emit(AvailableRidesLoaded(rides)),
    );
  }

  @override
  Future<void> close() {
    _webSocketSubscription?.cancel();
    return super.close();
  }
}
