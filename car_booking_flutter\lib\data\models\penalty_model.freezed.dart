// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'penalty_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PenaltyModel _$PenaltyModelFromJson(Map<String, dynamic> json) {
  return _PenaltyModel.fromJson(json);
}

/// @nodoc
mixin _$PenaltyModel {
  String get id => throw _privateConstructorUsedError;
  String get driverId => throw _privateConstructorUsedError;
  String? get rideId => throw _privateConstructorUsedError;
  PenaltyType get type => throw _privateConstructorUsedError;
  int get amount => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Serializes this PenaltyModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PenaltyModelCopyWith<PenaltyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PenaltyModelCopyWith<$Res> {
  factory $PenaltyModelCopyWith(
    PenaltyModel value,
    $Res Function(PenaltyModel) then,
  ) = _$PenaltyModelCopyWithImpl<$Res, PenaltyModel>;
  @useResult
  $Res call({
    String id,
    String driverId,
    String? rideId,
    PenaltyType type,
    int amount,
    String? reason,
    DateTime? createdAt,
  });
}

/// @nodoc
class _$PenaltyModelCopyWithImpl<$Res, $Val extends PenaltyModel>
    implements $PenaltyModelCopyWith<$Res> {
  _$PenaltyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? driverId = null,
    Object? rideId = freezed,
    Object? type = null,
    Object? amount = null,
    Object? reason = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            driverId: null == driverId
                ? _value.driverId
                : driverId // ignore: cast_nullable_to_non_nullable
                      as String,
            rideId: freezed == rideId
                ? _value.rideId
                : rideId // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PenaltyType,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as int,
            reason: freezed == reason
                ? _value.reason
                : reason // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PenaltyModelImplCopyWith<$Res>
    implements $PenaltyModelCopyWith<$Res> {
  factory _$$PenaltyModelImplCopyWith(
    _$PenaltyModelImpl value,
    $Res Function(_$PenaltyModelImpl) then,
  ) = __$$PenaltyModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String driverId,
    String? rideId,
    PenaltyType type,
    int amount,
    String? reason,
    DateTime? createdAt,
  });
}

/// @nodoc
class __$$PenaltyModelImplCopyWithImpl<$Res>
    extends _$PenaltyModelCopyWithImpl<$Res, _$PenaltyModelImpl>
    implements _$$PenaltyModelImplCopyWith<$Res> {
  __$$PenaltyModelImplCopyWithImpl(
    _$PenaltyModelImpl _value,
    $Res Function(_$PenaltyModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? driverId = null,
    Object? rideId = freezed,
    Object? type = null,
    Object? amount = null,
    Object? reason = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(
      _$PenaltyModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        driverId: null == driverId
            ? _value.driverId
            : driverId // ignore: cast_nullable_to_non_nullable
                  as String,
        rideId: freezed == rideId
            ? _value.rideId
            : rideId // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PenaltyType,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as int,
        reason: freezed == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PenaltyModelImpl implements _PenaltyModel {
  const _$PenaltyModelImpl({
    required this.id,
    required this.driverId,
    this.rideId,
    required this.type,
    required this.amount,
    this.reason,
    this.createdAt,
  });

  factory _$PenaltyModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PenaltyModelImplFromJson(json);

  @override
  final String id;
  @override
  final String driverId;
  @override
  final String? rideId;
  @override
  final PenaltyType type;
  @override
  final int amount;
  @override
  final String? reason;
  @override
  final DateTime? createdAt;

  @override
  String toString() {
    return 'PenaltyModel(id: $id, driverId: $driverId, rideId: $rideId, type: $type, amount: $amount, reason: $reason, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PenaltyModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.driverId, driverId) ||
                other.driverId == driverId) &&
            (identical(other.rideId, rideId) || other.rideId == rideId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    driverId,
    rideId,
    type,
    amount,
    reason,
    createdAt,
  );

  /// Create a copy of PenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PenaltyModelImplCopyWith<_$PenaltyModelImpl> get copyWith =>
      __$$PenaltyModelImplCopyWithImpl<_$PenaltyModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PenaltyModelImplToJson(this);
  }
}

abstract class _PenaltyModel implements PenaltyModel {
  const factory _PenaltyModel({
    required final String id,
    required final String driverId,
    final String? rideId,
    required final PenaltyType type,
    required final int amount,
    final String? reason,
    final DateTime? createdAt,
  }) = _$PenaltyModelImpl;

  factory _PenaltyModel.fromJson(Map<String, dynamic> json) =
      _$PenaltyModelImpl.fromJson;

  @override
  String get id;
  @override
  String get driverId;
  @override
  String? get rideId;
  @override
  PenaltyType get type;
  @override
  int get amount;
  @override
  String? get reason;
  @override
  DateTime? get createdAt;

  /// Create a copy of PenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PenaltyModelImplCopyWith<_$PenaltyModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreatePenaltyModel _$CreatePenaltyModelFromJson(Map<String, dynamic> json) {
  return _CreatePenaltyModel.fromJson(json);
}

/// @nodoc
mixin _$CreatePenaltyModel {
  String get driverId => throw _privateConstructorUsedError;
  String? get rideId => throw _privateConstructorUsedError;
  PenaltyType get type => throw _privateConstructorUsedError;
  int get amount => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;

  /// Serializes this CreatePenaltyModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreatePenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreatePenaltyModelCopyWith<CreatePenaltyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreatePenaltyModelCopyWith<$Res> {
  factory $CreatePenaltyModelCopyWith(
    CreatePenaltyModel value,
    $Res Function(CreatePenaltyModel) then,
  ) = _$CreatePenaltyModelCopyWithImpl<$Res, CreatePenaltyModel>;
  @useResult
  $Res call({
    String driverId,
    String? rideId,
    PenaltyType type,
    int amount,
    String? reason,
  });
}

/// @nodoc
class _$CreatePenaltyModelCopyWithImpl<$Res, $Val extends CreatePenaltyModel>
    implements $CreatePenaltyModelCopyWith<$Res> {
  _$CreatePenaltyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreatePenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? driverId = null,
    Object? rideId = freezed,
    Object? type = null,
    Object? amount = null,
    Object? reason = freezed,
  }) {
    return _then(
      _value.copyWith(
            driverId: null == driverId
                ? _value.driverId
                : driverId // ignore: cast_nullable_to_non_nullable
                      as String,
            rideId: freezed == rideId
                ? _value.rideId
                : rideId // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PenaltyType,
            amount: null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                      as int,
            reason: freezed == reason
                ? _value.reason
                : reason // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CreatePenaltyModelImplCopyWith<$Res>
    implements $CreatePenaltyModelCopyWith<$Res> {
  factory _$$CreatePenaltyModelImplCopyWith(
    _$CreatePenaltyModelImpl value,
    $Res Function(_$CreatePenaltyModelImpl) then,
  ) = __$$CreatePenaltyModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String driverId,
    String? rideId,
    PenaltyType type,
    int amount,
    String? reason,
  });
}

/// @nodoc
class __$$CreatePenaltyModelImplCopyWithImpl<$Res>
    extends _$CreatePenaltyModelCopyWithImpl<$Res, _$CreatePenaltyModelImpl>
    implements _$$CreatePenaltyModelImplCopyWith<$Res> {
  __$$CreatePenaltyModelImplCopyWithImpl(
    _$CreatePenaltyModelImpl _value,
    $Res Function(_$CreatePenaltyModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CreatePenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? driverId = null,
    Object? rideId = freezed,
    Object? type = null,
    Object? amount = null,
    Object? reason = freezed,
  }) {
    return _then(
      _$CreatePenaltyModelImpl(
        driverId: null == driverId
            ? _value.driverId
            : driverId // ignore: cast_nullable_to_non_nullable
                  as String,
        rideId: freezed == rideId
            ? _value.rideId
            : rideId // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PenaltyType,
        amount: null == amount
            ? _value.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as int,
        reason: freezed == reason
            ? _value.reason
            : reason // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CreatePenaltyModelImpl implements _CreatePenaltyModel {
  const _$CreatePenaltyModelImpl({
    required this.driverId,
    this.rideId,
    required this.type,
    required this.amount,
    this.reason,
  });

  factory _$CreatePenaltyModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreatePenaltyModelImplFromJson(json);

  @override
  final String driverId;
  @override
  final String? rideId;
  @override
  final PenaltyType type;
  @override
  final int amount;
  @override
  final String? reason;

  @override
  String toString() {
    return 'CreatePenaltyModel(driverId: $driverId, rideId: $rideId, type: $type, amount: $amount, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreatePenaltyModelImpl &&
            (identical(other.driverId, driverId) ||
                other.driverId == driverId) &&
            (identical(other.rideId, rideId) || other.rideId == rideId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, driverId, rideId, type, amount, reason);

  /// Create a copy of CreatePenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreatePenaltyModelImplCopyWith<_$CreatePenaltyModelImpl> get copyWith =>
      __$$CreatePenaltyModelImplCopyWithImpl<_$CreatePenaltyModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CreatePenaltyModelImplToJson(this);
  }
}

abstract class _CreatePenaltyModel implements CreatePenaltyModel {
  const factory _CreatePenaltyModel({
    required final String driverId,
    final String? rideId,
    required final PenaltyType type,
    required final int amount,
    final String? reason,
  }) = _$CreatePenaltyModelImpl;

  factory _CreatePenaltyModel.fromJson(Map<String, dynamic> json) =
      _$CreatePenaltyModelImpl.fromJson;

  @override
  String get driverId;
  @override
  String? get rideId;
  @override
  PenaltyType get type;
  @override
  int get amount;
  @override
  String? get reason;

  /// Create a copy of CreatePenaltyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreatePenaltyModelImplCopyWith<_$CreatePenaltyModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
