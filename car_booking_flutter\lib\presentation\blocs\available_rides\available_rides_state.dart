import 'package:equatable/equatable.dart';
import '../../../domain/entities/ride.dart';

abstract class AvailableRidesState extends Equatable {
  const AvailableRidesState();

  @override
  List<Object?> get props => [];
}

class AvailableRidesInitial extends AvailableRidesState {}

class AvailableRidesLoading extends AvailableRidesState {}

class AvailableRidesLoaded extends AvailableRidesState {
  const AvailableRidesLoaded(this.rides);

  final List<RideWithDrivers> rides;

  @override
  List<Object?> get props => [rides];
}

class AvailableRidesError extends AvailableRidesState {
  const AvailableRidesError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

class AvailableRidesAccepting extends AvailableRidesState {
  const AvailableRidesAccepting(this.rideId);

  final String rideId;

  @override
  List<Object?> get props => [rideId];
}

class AvailableRidesAcceptSuccess extends AvailableRidesState {
  const AvailableRidesAcceptSuccess(this.rideId);

  final String rideId;

  @override
  List<Object?> get props => [rideId];
}

class AvailableRidesAcceptError extends AvailableRidesState {
  const AvailableRidesAcceptError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}
