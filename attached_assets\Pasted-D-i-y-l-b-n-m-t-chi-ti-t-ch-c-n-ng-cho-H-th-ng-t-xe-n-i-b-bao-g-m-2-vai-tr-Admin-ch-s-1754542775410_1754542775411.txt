Dưới đây là bản mô tả chi tiết chức năng cho Hệ thống đặt xe nội bộ, bao gồm 2 vai trò: <PERSON><PERSON> (chủ sàn) và Tài xế (chủ xe hoặc nhận cuốc).

1. MÔ TẢ TỔNG QUAN DỰ ÁN
Hệ thống đặt xe nội bộ cho phép các tài xế tạo, nhận và thực hiện cuốc xe trong phạm vi tổ chức hoặc sàn điều hành. Mục tiêu là tối ưu hóa lịch trình xe, kết nối tài xế nội bộ và đảm bảo thông tin cuốc xe minh bạch, rõ ràng. Nền tảng hoạt động trên cơ chế phân quyền giữa Admin điều hành, Tài xế tạo cuốc xe, và Tài xế nhận cuốc xe.

2. LUỒNG TÍNH NĂNG NGẮN GỌN
Tài xế 1 tạo cuốc xe → hệ thống hiển thị cuốc xe cho tài xế khác trong khu vực → Tài xế 2 nhận cuốc xe → thực hiện chuyến đi.


Admin có thể cấu hình các thông số: phí hoa hồng, cấu hình phạt, giám sát lịch trình, thống kê.



3. LỢI ÍCH PHẦN MỀM
🧠 Tự động hóa việc phân phối cuốc xe theo vị trí định vị.


👁️ Giao diện đơn giản, dễ sử dụng khi đang lái xe.


🚨 Giảm sai sót trong việc quên đón khách nhờ logic cảnh báo và phạt.


📊 Tùy chỉnh phí, phạt, hoa hồng linh hoạt cho Admin.


⏱️ Cập nhật cuốc xe theo thời gian thực (auto-refresh mỗi 5 giây).



4. PHÂN TÍCH CHI TIẾT CHỨC NĂNG

A. TRANG QUẢN TRỊ ADMIN (CHỦ SÀN)
I. Quản lý cuốc xe
Danh sách tất cả các cuốc xe đã được tạo


Trạng thái: Đang chờ nhận / Đang thực hiện / Đã hoàn thành / Bị hủy


Lọc theo:


Tài xế tạo


Tài xế nhận


Ngày / thời gian


Khu vực


II. Cấu hình hệ thống
Cấu hình hoa hồng mặc định cho mỗi cuốc xe (theo %)


Cấu hình phí phạt:


Hủy cuốc do sai sót


Không đón khách


Ngủ quên, quên đón khách


Cấu hình phí chia sẻ giữa chủ xe tạo cuốc & tài xế nhận


III. Quản lý tài xế
Danh sách tài xế: trạng thái hoạt động / trạng thái nhận cuốc / vị trí hiện tại


Cấu hình tài khoản: thông tin cá nhân, liên hệ, tài khoản ngân hàng


Thống kê số cuốc xe đã chạy, lịch sử cuốc


IV. Quản lý báo cáo
Thống kê số lượng cuốc xe mỗi ngày / tuần / tháng


Doanh thu theo cuốc xe


Tổng phí thu được từ hoa hồng và phạt


V. Quản lý khu vực & bản đồ
Cập nhật bản đồ hành chính: Tỉnh / Huyện / Xã


Cấu hình phân vùng khu vực ưu tiên theo định vị



B. TRANG QUẢN TRỊ TÀI XẾ (APP/MOBILE)
I. Tạo cuốc xe mới
Chọn loại xe: 4 chỗ / 7 chỗ / 9 chỗ / 16 chỗ


Chọn ngày giờ đón khách:


Mặc định giờ hiện tại


Tùy chọn "Đi ngay càng sớm càng tốt" → hệ thống gán trong 10–20 phút


Nhập:


Giá cuốc xe


Tỷ lệ hoa hồng cho người tạo


Thông tin liên hệ:


Zalo của chủ cuốc xe


Địa chỉ đón khách


Zalo của người cần đón (người nhận dịch vụ)


II. Xem & nhận cuốc xe (dành cho tài xế khác)
Danh sách cuốc xe khả dụng (hiển thị to, dễ nhìn, rõ ràng)


Auto lọc & tự động cập nhật mỗi 5 giây:


Ưu tiên hiển thị theo: Xã → Huyện → Tỉnh


Dựa trên định vị GPS hiện tại


Chỉ cần 1 nút “Nhận cuốc” để xác nhận


Thông tin hiển thị:


Thời gian, địa điểm đón


Giá, phí hoa hồng


Zalo liên hệ


Xe phù hợp (loại xe cần)


III. Lịch sử cuốc xe
Danh sách cuốc xe đã tạo hoặc đã nhận


Trạng thái từng cuốc xe


Ghi chú lý do huỷ (nếu có)


IV. Giao diện cảnh báo & phạt
Cảnh báo tài xế khi sắp tới giờ đón


Ghi nhận lý do hủy, không nhận, không đến → trigger hệ thống phạt


Hiển thị thông báo phạt và mức phí



5. THIẾT KẾ GIAO DIỆN (UX/UI)
Thiết kế giao diện: Đơn giản, tối ưu cho thiết bị di động, chữ lớn, dễ thao tác khi đang lái xe.


Tài xế không cần thao tác nhiều – chỉ cần nhìn và bấm 1 nút “Nhận cuốc”.


Màu sắc phân biệt trạng thái cuốc xe (ví dụ: Đang chờ – Xanh, Đã nhận – Cam, Hoàn thành – Xám).



6. NỀN TẢNG CÔNG NGHỆ
Platform: Web-based cho Admin, App Mobile (Flutter) cho Tài xế


Backend: Laravel


Database: MySQL


Realtime: WebSocket (Socket.io hoặc Firebase Realtime)


Location: Tích hợp Google Maps API / OpenStreetMap
