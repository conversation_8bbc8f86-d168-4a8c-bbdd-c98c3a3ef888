import 'package:intl/intl.dart';
import '../constants/enums.dart';

class Formatters {
  // Currency formatter for Vietnamese Dong
  static String formatCurrency(int amount) {
    final formatter = NumberFormat('#,###', 'vi_VN');
    return '${formatter.format(amount)} VNĐ';
  }

  // Short currency formatter (e.g., 1.2M VNĐ)
  static String formatCurrencyShort(int amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M VNĐ';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K VNĐ';
    } else {
      return '$amount VNĐ';
    }
  }

  // Date and time formatters
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm', 'vi_VN').format(dateTime);
  }

  static String formatDate(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy', 'vi_VN').format(dateTime);
  }

  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm', 'vi_VN').format(dateTime);
  }

  // Relative time formatter
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Hôm qua';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} ngày trước';
      } else {
        return formatDate(dateTime);
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  // Phone number formatter
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    if (digits.length == 10) {
      // Format as: 0xxx xxx xxx
      return '${digits.substring(0, 4)} ${digits.substring(4, 7)} ${digits.substring(7)}';
    } else if (digits.length == 11 && digits.startsWith('84')) {
      // Format as: +84 xxx xxx xxx
      return '+84 ${digits.substring(2, 5)} ${digits.substring(5, 8)} ${digits.substring(8)}';
    }
    
    return phoneNumber; // Return original if format is not recognized
  }

  // Vehicle type formatter
  static String formatVehicleType(VehicleType vehicleType) {
    return vehicleType.displayName;
  }

  // Status formatters
  static String formatRideStatus(RideStatus status) {
    return status.displayName;
  }

  static String formatDriverStatus(DriverStatus status) {
    return status.displayName;
  }

  static String formatPenaltyType(PenaltyType type) {
    return type.displayName;
  }

  // Percentage formatter
  static String formatPercentage(int percentage) {
    return '$percentage%';
  }

  // Distance formatter (if needed in the future)
  static String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      return '${(distanceInKm * 1000).round()}m';
    } else {
      return '${distanceInKm.toStringAsFixed(1)}km';
    }
  }

  // Duration formatter
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  // Address formatter
  static String formatAddress(String? commune, String? district, String? province) {
    final parts = <String>[];
    if (commune != null && commune.isNotEmpty) parts.add(commune);
    if (district != null && district.isNotEmpty) parts.add(district);
    if (province != null && province.isNotEmpty) parts.add(province);
    
    return parts.join(', ');
  }

  // Truncate text
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }
}
