import { useQuery } from "@tanstack/react-query";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { MapPin, Clock, MessageCircle, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import StatusBadge from "@/components/ui/status-badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { RideWithDrivers } from "@shared/schema";

export default function AvailableRides() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const currentDriverId = 'demo-driver-id'; // In real app, get from auth context

  const { data: rides, isLoading, isFetching } = useQuery({
    queryKey: ['/api/rides/available'],
    refetchInterval: false, // Disable auto-refetch, use parent component's timer instead
    staleTime: 4000, // Consider data stale after 4 seconds
  });

  const acceptRideMutation = useMutation({
    mutationFn: async (rideId: string) => {
      return apiRequest('POST', `/api/rides/${rideId}/accept`, { driverId: currentDriverId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/rides/available'] });
      toast({
        title: "Thành công",
        description: "Đã nhận cuốc xe thành công!",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể nhận cuốc xe. Vui lòng thử lại.",
        variant: "destructive",
      });
    },
  });

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + ' VNĐ';
  };

  const formatTime = (date: string) => {
    return new Date(date).toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getPriorityColor = (commune?: string | null, district?: string | null, province?: string | null) => {
    // This would be based on driver's current location in real app
    if (commune === 'Bến Nghé') return 'green'; // Same commune
    if (district === 'Quận 1') return 'yellow'; // Same district  
    if (province === 'TP.HCM') return 'gray'; // Same province
    return 'gray';
  };

  const getPriorityLabel = (commune?: string | null, district?: string | null, province?: string | null) => {
    if (commune === 'Bến Nghé') return 'Cùng xã';
    if (district === 'Quận 1') return 'Cùng huyện';
    if (province === 'TP.HCM') return 'Cùng tỉnh';
    return 'Khác tỉnh';
  };

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-200 rounded-xl h-40"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">
          Cuốc Xe Gần Bạn
          {isFetching && (
            <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-1"></div>
              Đang cập nhật
            </span>
          )}
        </h2>
        <span className="text-sm text-gray-500">
          <MapPin className="w-4 h-4 mr-1 inline" />
          Theo vị trí GPS
        </span>
      </div>

      {!rides || (rides as RideWithDrivers[])?.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>Chưa có cuốc xe khả dụng</p>
          <p className="text-sm">Hệ thống sẽ tự động cập nhật</p>
        </div>
      ) : (
        <div className="space-y-4">
          {(rides as RideWithDrivers[]).map((ride) => {
            const priorityColor = getPriorityColor(ride.commune, ride.district, ride.province);
            const priorityLabel = getPriorityLabel(ride.commune, ride.district, ride.province);
            
            return (
              <div
                key={ride.id}
                className={`border-2 rounded-xl p-4 shadow-sm ${
                  priorityColor === 'green' 
                    ? 'bg-green-50 border-green-200'
                    : priorityColor === 'yellow'
                    ? 'bg-yellow-50 border-yellow-200'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <StatusBadge 
                        status={priorityLabel}
                        variant={priorityColor as 'green' | 'yellow' | 'gray'}
                      />
                      <StatusBadge 
                        status={ride.vehicleType.replace('_', ' ')}
                        variant="blue"
                        className="ml-2"
                      />
                    </div>
                    <p className="font-semibold text-lg text-gray-900 mb-1">
                      {formatCurrency(ride.price)}
                    </p>
                    <p className="text-sm text-gray-600 mb-1">
                      <Clock className="w-4 h-4 mr-1 inline" />
                      {formatTime(ride.pickupTime.toString())} - {ride.isAsap ? 'Đi ngay' : 'Hôm nay'}
                    </p>
                    <p className="text-sm text-gray-600 mb-2">
                      <MapPin className="w-4 h-4 mr-1 inline" />
                      {ride.pickupAddress}
                    </p>
                    <p className="text-sm text-gray-500">
                      <MessageCircle className="w-4 h-4 mr-1 inline" />
                      Zalo: {ride.passengerZalo}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between pt-3 border-t border-current border-opacity-20">
                  <div className="text-sm">
                    <span className="text-gray-600">Hoa hồng:</span>
                    <span className={`font-semibold ml-1 ${
                      priorityColor === 'green' ? 'text-green-600' :
                      priorityColor === 'yellow' ? 'text-yellow-600' : 'text-gray-600'
                    }`}>
                      {formatCurrency(ride.commissionAmount)} ({ride.commissionRate}%)
                    </span>
                  </div>
                  <Button
                    onClick={() => acceptRideMutation.mutate(ride.id)}
                    disabled={acceptRideMutation.isPending}
                    className={`px-6 py-3 rounded-lg font-semibold text-base transition-colors min-h-[48px] ${
                      priorityColor === 'green' 
                        ? 'bg-green-600 hover:bg-green-700'
                        : priorityColor === 'yellow'
                        ? 'bg-yellow-600 hover:bg-yellow-700'
                        : 'bg-gray-600 hover:bg-gray-700'
                    } text-white`}
                  >
                    <Check className="w-4 h-4 mr-2" />
                    {acceptRideMutation.isPending ? 'Đang xử lý...' : 'Nhận Cuốc'}
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
