import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/ride.dart';
import '../../../domain/usecases/get_ride_history.dart';

// States
abstract class RideHistoryState extends Equatable {
  const RideHistoryState();

  @override
  List<Object?> get props => [];
}

class RideHistoryInitial extends RideHistoryState {}

class RideHistoryLoading extends RideHistoryState {}

class RideHistoryLoaded extends RideHistoryState {
  const RideHistoryLoaded(this.rides);

  final List<RideWithDrivers> rides;

  @override
  List<Object?> get props => [rides];
}

class RideHistoryError extends RideHistoryState {
  const RideHistoryError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

// Cubit
class RideHistoryCubit extends Cubit<RideHistoryState> {
  final GetRideHistoryUseCase _getRideHistory;

  RideHistoryCubit({
    required GetRideHistoryUseCase getRideHistory,
  })  : _getRideHistory = getRideHistory,
        super(RideHistoryInitial());

  Future<void> loadRideHistory(String driverId) async {
    emit(RideHistoryLoading());
    
    final result = await _getRideHistory(driverId);
    
    result.fold(
      (failure) => emit(RideHistoryError(failure.message)),
      (rides) => emit(RideHistoryLoaded(rides)),
    );
  }

  Future<void> refreshRideHistory(String driverId) async {
    // Don't show loading state for refresh
    final result = await _getRideHistory(driverId);
    
    result.fold(
      (failure) => emit(RideHistoryError(failure.message)),
      (rides) => emit(RideHistoryLoaded(rides)),
    );
  }
}
