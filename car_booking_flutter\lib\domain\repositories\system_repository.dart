import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../entities/system_config.dart';
import '../entities/dashboard_stats.dart';
import '../entities/penalty.dart';

abstract class SystemRepository {
  Future<Either<Failure, SystemConfig>> getSystemConfig();
  Future<Either<Failure, SystemConfig>> updateSystemConfig(UpdateSystemConfig config);
  Future<Either<Failure, DashboardStats>> getDashboardStats();
  Future<Either<Failure, List<Penalty>>> getDriverPenalties(String driverId);
  Future<Either<Failure, Penalty>> createPenalty(CreatePenalty penalty);
}
