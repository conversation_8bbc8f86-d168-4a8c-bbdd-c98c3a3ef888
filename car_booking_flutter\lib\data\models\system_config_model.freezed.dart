// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'system_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SystemConfigModel _$SystemConfigModelFromJson(Map<String, dynamic> json) {
  return _SystemConfigModel.fromJson(json);
}

/// @nodoc
mixin _$SystemConfigModel {
  String get id => throw _privateConstructorUsedError;
  int get defaultCommissionRate => throw _privateConstructorUsedError;
  int get driverSharePercentage => throw _privateConstructorUsedError;
  int get cancelPenalty => throw _privateConstructorUsedError;
  int get noPickupPenalty => throw _privateConstructorUsedError;
  int get missedPickupPenalty => throw _privateConstructorUsedError;
  int get warningTimeMinutes => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this SystemConfigModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SystemConfigModelCopyWith<SystemConfigModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SystemConfigModelCopyWith<$Res> {
  factory $SystemConfigModelCopyWith(
    SystemConfigModel value,
    $Res Function(SystemConfigModel) then,
  ) = _$SystemConfigModelCopyWithImpl<$Res, SystemConfigModel>;
  @useResult
  $Res call({
    String id,
    int defaultCommissionRate,
    int driverSharePercentage,
    int cancelPenalty,
    int noPickupPenalty,
    int missedPickupPenalty,
    int warningTimeMinutes,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$SystemConfigModelCopyWithImpl<$Res, $Val extends SystemConfigModel>
    implements $SystemConfigModelCopyWith<$Res> {
  _$SystemConfigModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? defaultCommissionRate = null,
    Object? driverSharePercentage = null,
    Object? cancelPenalty = null,
    Object? noPickupPenalty = null,
    Object? missedPickupPenalty = null,
    Object? warningTimeMinutes = null,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            defaultCommissionRate: null == defaultCommissionRate
                ? _value.defaultCommissionRate
                : defaultCommissionRate // ignore: cast_nullable_to_non_nullable
                      as int,
            driverSharePercentage: null == driverSharePercentage
                ? _value.driverSharePercentage
                : driverSharePercentage // ignore: cast_nullable_to_non_nullable
                      as int,
            cancelPenalty: null == cancelPenalty
                ? _value.cancelPenalty
                : cancelPenalty // ignore: cast_nullable_to_non_nullable
                      as int,
            noPickupPenalty: null == noPickupPenalty
                ? _value.noPickupPenalty
                : noPickupPenalty // ignore: cast_nullable_to_non_nullable
                      as int,
            missedPickupPenalty: null == missedPickupPenalty
                ? _value.missedPickupPenalty
                : missedPickupPenalty // ignore: cast_nullable_to_non_nullable
                      as int,
            warningTimeMinutes: null == warningTimeMinutes
                ? _value.warningTimeMinutes
                : warningTimeMinutes // ignore: cast_nullable_to_non_nullable
                      as int,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SystemConfigModelImplCopyWith<$Res>
    implements $SystemConfigModelCopyWith<$Res> {
  factory _$$SystemConfigModelImplCopyWith(
    _$SystemConfigModelImpl value,
    $Res Function(_$SystemConfigModelImpl) then,
  ) = __$$SystemConfigModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    int defaultCommissionRate,
    int driverSharePercentage,
    int cancelPenalty,
    int noPickupPenalty,
    int missedPickupPenalty,
    int warningTimeMinutes,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$SystemConfigModelImplCopyWithImpl<$Res>
    extends _$SystemConfigModelCopyWithImpl<$Res, _$SystemConfigModelImpl>
    implements _$$SystemConfigModelImplCopyWith<$Res> {
  __$$SystemConfigModelImplCopyWithImpl(
    _$SystemConfigModelImpl _value,
    $Res Function(_$SystemConfigModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? defaultCommissionRate = null,
    Object? driverSharePercentage = null,
    Object? cancelPenalty = null,
    Object? noPickupPenalty = null,
    Object? missedPickupPenalty = null,
    Object? warningTimeMinutes = null,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$SystemConfigModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        defaultCommissionRate: null == defaultCommissionRate
            ? _value.defaultCommissionRate
            : defaultCommissionRate // ignore: cast_nullable_to_non_nullable
                  as int,
        driverSharePercentage: null == driverSharePercentage
            ? _value.driverSharePercentage
            : driverSharePercentage // ignore: cast_nullable_to_non_nullable
                  as int,
        cancelPenalty: null == cancelPenalty
            ? _value.cancelPenalty
            : cancelPenalty // ignore: cast_nullable_to_non_nullable
                  as int,
        noPickupPenalty: null == noPickupPenalty
            ? _value.noPickupPenalty
            : noPickupPenalty // ignore: cast_nullable_to_non_nullable
                  as int,
        missedPickupPenalty: null == missedPickupPenalty
            ? _value.missedPickupPenalty
            : missedPickupPenalty // ignore: cast_nullable_to_non_nullable
                  as int,
        warningTimeMinutes: null == warningTimeMinutes
            ? _value.warningTimeMinutes
            : warningTimeMinutes // ignore: cast_nullable_to_non_nullable
                  as int,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SystemConfigModelImpl implements _SystemConfigModel {
  const _$SystemConfigModelImpl({
    required this.id,
    this.defaultCommissionRate = 10,
    this.driverSharePercentage = 70,
    this.cancelPenalty = 25000,
    this.noPickupPenalty = 50000,
    this.missedPickupPenalty = 75000,
    this.warningTimeMinutes = 15,
    this.updatedAt,
  });

  factory _$SystemConfigModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SystemConfigModelImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey()
  final int defaultCommissionRate;
  @override
  @JsonKey()
  final int driverSharePercentage;
  @override
  @JsonKey()
  final int cancelPenalty;
  @override
  @JsonKey()
  final int noPickupPenalty;
  @override
  @JsonKey()
  final int missedPickupPenalty;
  @override
  @JsonKey()
  final int warningTimeMinutes;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'SystemConfigModel(id: $id, defaultCommissionRate: $defaultCommissionRate, driverSharePercentage: $driverSharePercentage, cancelPenalty: $cancelPenalty, noPickupPenalty: $noPickupPenalty, missedPickupPenalty: $missedPickupPenalty, warningTimeMinutes: $warningTimeMinutes, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SystemConfigModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.defaultCommissionRate, defaultCommissionRate) ||
                other.defaultCommissionRate == defaultCommissionRate) &&
            (identical(other.driverSharePercentage, driverSharePercentage) ||
                other.driverSharePercentage == driverSharePercentage) &&
            (identical(other.cancelPenalty, cancelPenalty) ||
                other.cancelPenalty == cancelPenalty) &&
            (identical(other.noPickupPenalty, noPickupPenalty) ||
                other.noPickupPenalty == noPickupPenalty) &&
            (identical(other.missedPickupPenalty, missedPickupPenalty) ||
                other.missedPickupPenalty == missedPickupPenalty) &&
            (identical(other.warningTimeMinutes, warningTimeMinutes) ||
                other.warningTimeMinutes == warningTimeMinutes) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    defaultCommissionRate,
    driverSharePercentage,
    cancelPenalty,
    noPickupPenalty,
    missedPickupPenalty,
    warningTimeMinutes,
    updatedAt,
  );

  /// Create a copy of SystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SystemConfigModelImplCopyWith<_$SystemConfigModelImpl> get copyWith =>
      __$$SystemConfigModelImplCopyWithImpl<_$SystemConfigModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SystemConfigModelImplToJson(this);
  }
}

abstract class _SystemConfigModel implements SystemConfigModel {
  const factory _SystemConfigModel({
    required final String id,
    final int defaultCommissionRate,
    final int driverSharePercentage,
    final int cancelPenalty,
    final int noPickupPenalty,
    final int missedPickupPenalty,
    final int warningTimeMinutes,
    final DateTime? updatedAt,
  }) = _$SystemConfigModelImpl;

  factory _SystemConfigModel.fromJson(Map<String, dynamic> json) =
      _$SystemConfigModelImpl.fromJson;

  @override
  String get id;
  @override
  int get defaultCommissionRate;
  @override
  int get driverSharePercentage;
  @override
  int get cancelPenalty;
  @override
  int get noPickupPenalty;
  @override
  int get missedPickupPenalty;
  @override
  int get warningTimeMinutes;
  @override
  DateTime? get updatedAt;

  /// Create a copy of SystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SystemConfigModelImplCopyWith<_$SystemConfigModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UpdateSystemConfigModel _$UpdateSystemConfigModelFromJson(
  Map<String, dynamic> json,
) {
  return _UpdateSystemConfigModel.fromJson(json);
}

/// @nodoc
mixin _$UpdateSystemConfigModel {
  int? get defaultCommissionRate => throw _privateConstructorUsedError;
  int? get driverSharePercentage => throw _privateConstructorUsedError;
  int? get cancelPenalty => throw _privateConstructorUsedError;
  int? get noPickupPenalty => throw _privateConstructorUsedError;
  int? get missedPickupPenalty => throw _privateConstructorUsedError;
  int? get warningTimeMinutes => throw _privateConstructorUsedError;

  /// Serializes this UpdateSystemConfigModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateSystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateSystemConfigModelCopyWith<UpdateSystemConfigModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateSystemConfigModelCopyWith<$Res> {
  factory $UpdateSystemConfigModelCopyWith(
    UpdateSystemConfigModel value,
    $Res Function(UpdateSystemConfigModel) then,
  ) = _$UpdateSystemConfigModelCopyWithImpl<$Res, UpdateSystemConfigModel>;
  @useResult
  $Res call({
    int? defaultCommissionRate,
    int? driverSharePercentage,
    int? cancelPenalty,
    int? noPickupPenalty,
    int? missedPickupPenalty,
    int? warningTimeMinutes,
  });
}

/// @nodoc
class _$UpdateSystemConfigModelCopyWithImpl<
  $Res,
  $Val extends UpdateSystemConfigModel
>
    implements $UpdateSystemConfigModelCopyWith<$Res> {
  _$UpdateSystemConfigModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateSystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? defaultCommissionRate = freezed,
    Object? driverSharePercentage = freezed,
    Object? cancelPenalty = freezed,
    Object? noPickupPenalty = freezed,
    Object? missedPickupPenalty = freezed,
    Object? warningTimeMinutes = freezed,
  }) {
    return _then(
      _value.copyWith(
            defaultCommissionRate: freezed == defaultCommissionRate
                ? _value.defaultCommissionRate
                : defaultCommissionRate // ignore: cast_nullable_to_non_nullable
                      as int?,
            driverSharePercentage: freezed == driverSharePercentage
                ? _value.driverSharePercentage
                : driverSharePercentage // ignore: cast_nullable_to_non_nullable
                      as int?,
            cancelPenalty: freezed == cancelPenalty
                ? _value.cancelPenalty
                : cancelPenalty // ignore: cast_nullable_to_non_nullable
                      as int?,
            noPickupPenalty: freezed == noPickupPenalty
                ? _value.noPickupPenalty
                : noPickupPenalty // ignore: cast_nullable_to_non_nullable
                      as int?,
            missedPickupPenalty: freezed == missedPickupPenalty
                ? _value.missedPickupPenalty
                : missedPickupPenalty // ignore: cast_nullable_to_non_nullable
                      as int?,
            warningTimeMinutes: freezed == warningTimeMinutes
                ? _value.warningTimeMinutes
                : warningTimeMinutes // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UpdateSystemConfigModelImplCopyWith<$Res>
    implements $UpdateSystemConfigModelCopyWith<$Res> {
  factory _$$UpdateSystemConfigModelImplCopyWith(
    _$UpdateSystemConfigModelImpl value,
    $Res Function(_$UpdateSystemConfigModelImpl) then,
  ) = __$$UpdateSystemConfigModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? defaultCommissionRate,
    int? driverSharePercentage,
    int? cancelPenalty,
    int? noPickupPenalty,
    int? missedPickupPenalty,
    int? warningTimeMinutes,
  });
}

/// @nodoc
class __$$UpdateSystemConfigModelImplCopyWithImpl<$Res>
    extends
        _$UpdateSystemConfigModelCopyWithImpl<
          $Res,
          _$UpdateSystemConfigModelImpl
        >
    implements _$$UpdateSystemConfigModelImplCopyWith<$Res> {
  __$$UpdateSystemConfigModelImplCopyWithImpl(
    _$UpdateSystemConfigModelImpl _value,
    $Res Function(_$UpdateSystemConfigModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UpdateSystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? defaultCommissionRate = freezed,
    Object? driverSharePercentage = freezed,
    Object? cancelPenalty = freezed,
    Object? noPickupPenalty = freezed,
    Object? missedPickupPenalty = freezed,
    Object? warningTimeMinutes = freezed,
  }) {
    return _then(
      _$UpdateSystemConfigModelImpl(
        defaultCommissionRate: freezed == defaultCommissionRate
            ? _value.defaultCommissionRate
            : defaultCommissionRate // ignore: cast_nullable_to_non_nullable
                  as int?,
        driverSharePercentage: freezed == driverSharePercentage
            ? _value.driverSharePercentage
            : driverSharePercentage // ignore: cast_nullable_to_non_nullable
                  as int?,
        cancelPenalty: freezed == cancelPenalty
            ? _value.cancelPenalty
            : cancelPenalty // ignore: cast_nullable_to_non_nullable
                  as int?,
        noPickupPenalty: freezed == noPickupPenalty
            ? _value.noPickupPenalty
            : noPickupPenalty // ignore: cast_nullable_to_non_nullable
                  as int?,
        missedPickupPenalty: freezed == missedPickupPenalty
            ? _value.missedPickupPenalty
            : missedPickupPenalty // ignore: cast_nullable_to_non_nullable
                  as int?,
        warningTimeMinutes: freezed == warningTimeMinutes
            ? _value.warningTimeMinutes
            : warningTimeMinutes // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateSystemConfigModelImpl implements _UpdateSystemConfigModel {
  const _$UpdateSystemConfigModelImpl({
    this.defaultCommissionRate,
    this.driverSharePercentage,
    this.cancelPenalty,
    this.noPickupPenalty,
    this.missedPickupPenalty,
    this.warningTimeMinutes,
  });

  factory _$UpdateSystemConfigModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateSystemConfigModelImplFromJson(json);

  @override
  final int? defaultCommissionRate;
  @override
  final int? driverSharePercentage;
  @override
  final int? cancelPenalty;
  @override
  final int? noPickupPenalty;
  @override
  final int? missedPickupPenalty;
  @override
  final int? warningTimeMinutes;

  @override
  String toString() {
    return 'UpdateSystemConfigModel(defaultCommissionRate: $defaultCommissionRate, driverSharePercentage: $driverSharePercentage, cancelPenalty: $cancelPenalty, noPickupPenalty: $noPickupPenalty, missedPickupPenalty: $missedPickupPenalty, warningTimeMinutes: $warningTimeMinutes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateSystemConfigModelImpl &&
            (identical(other.defaultCommissionRate, defaultCommissionRate) ||
                other.defaultCommissionRate == defaultCommissionRate) &&
            (identical(other.driverSharePercentage, driverSharePercentage) ||
                other.driverSharePercentage == driverSharePercentage) &&
            (identical(other.cancelPenalty, cancelPenalty) ||
                other.cancelPenalty == cancelPenalty) &&
            (identical(other.noPickupPenalty, noPickupPenalty) ||
                other.noPickupPenalty == noPickupPenalty) &&
            (identical(other.missedPickupPenalty, missedPickupPenalty) ||
                other.missedPickupPenalty == missedPickupPenalty) &&
            (identical(other.warningTimeMinutes, warningTimeMinutes) ||
                other.warningTimeMinutes == warningTimeMinutes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    defaultCommissionRate,
    driverSharePercentage,
    cancelPenalty,
    noPickupPenalty,
    missedPickupPenalty,
    warningTimeMinutes,
  );

  /// Create a copy of UpdateSystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateSystemConfigModelImplCopyWith<_$UpdateSystemConfigModelImpl>
  get copyWith =>
      __$$UpdateSystemConfigModelImplCopyWithImpl<
        _$UpdateSystemConfigModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateSystemConfigModelImplToJson(this);
  }
}

abstract class _UpdateSystemConfigModel implements UpdateSystemConfigModel {
  const factory _UpdateSystemConfigModel({
    final int? defaultCommissionRate,
    final int? driverSharePercentage,
    final int? cancelPenalty,
    final int? noPickupPenalty,
    final int? missedPickupPenalty,
    final int? warningTimeMinutes,
  }) = _$UpdateSystemConfigModelImpl;

  factory _UpdateSystemConfigModel.fromJson(Map<String, dynamic> json) =
      _$UpdateSystemConfigModelImpl.fromJson;

  @override
  int? get defaultCommissionRate;
  @override
  int? get driverSharePercentage;
  @override
  int? get cancelPenalty;
  @override
  int? get noPickupPenalty;
  @override
  int? get missedPickupPenalty;
  @override
  int? get warningTimeMinutes;

  /// Create a copy of UpdateSystemConfigModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateSystemConfigModelImplCopyWith<_$UpdateSystemConfigModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
