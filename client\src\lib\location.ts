export interface LocationInfo {
  province: string;
  district: string;
  commune: string;
}

export function getCurrentLocation(): Promise<LocationInfo> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          // In a real app, you would use a geocoding service like Google Maps API
          // For now, return mock location based on coordinates
          const mockLocation: LocationInfo = {
            province: 'TP.HCM',
            district: 'Quận 1',
            commune: 'Bến Nghé',
          };
          resolve(mockLocation);
        } catch (error) {
          reject(error);
        }
      },
      (error) => {
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  });
}

export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Radius of the Earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

export function getPriorityByLocation(
  userLocation: LocationInfo,
  rideLocation: LocationInfo
): { priority: number; label: string; color: string } {
  if (userLocation.commune === rideLocation.commune) {
    return { priority: 1, label: 'Cùng xã', color: 'green' };
  }
  if (userLocation.district === rideLocation.district) {
    return { priority: 2, label: 'Cùng huyện', color: 'yellow' };
  }
  if (userLocation.province === rideLocation.province) {
    return { priority: 3, label: 'Cùng tỉnh', color: 'gray' };
  }
  return { priority: 4, label: 'Khác tỉnh', color: 'gray' };
}
