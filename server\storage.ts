import { 
  drivers, 
  rides, 
  systemConfig, 
  penalties,
  type Driver, 
  type InsertDriver, 
  type Ride, 
  type InsertRide,
  type SystemConfig,
  type InsertSystemConfig,
  type Penalty,
  type InsertPenalty,
  type RideWithDrivers,
  type DriverWithStats
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, sql, count } from "drizzle-orm";
import { randomUUID } from "crypto";

export interface IStorage {
  // Driver methods
  getDriver(id: string): Promise<Driver | undefined>;
  getDriverByPhone(phone: string): Promise<Driver | undefined>;
  createDriver(driver: InsertDriver): Promise<Driver>;
  updateDriverStatus(id: string, status: 'active' | 'inactive' | 'busy'): Promise<void>;
  updateDriverLocation(id: string, location: string): Promise<void>;
  getAllDrivers(): Promise<DriverWithStats[]>;

  // Ride methods
  getRide(id: string): Promise<RideWithDrivers | undefined>;
  createRide(ride: InsertRide): Promise<Ride>;
  getAvailableRides(driverLocation?: { province?: string; district?: string; commune?: string }): Promise<RideWithDrivers[]>;
  acceptRide(rideId: string, driverId: string): Promise<void>;
  updateRideStatus(id: string, status: 'waiting' | 'accepted' | 'in_progress' | 'completed' | 'cancelled'): Promise<void>;
  getRideHistory(driverId: string): Promise<RideWithDrivers[]>;
  getAllRides(): Promise<RideWithDrivers[]>;

  // System config methods
  getSystemConfig(): Promise<SystemConfig>;
  updateSystemConfig(config: Partial<InsertSystemConfig>): Promise<SystemConfig>;

  // Penalty methods
  createPenalty(penalty: InsertPenalty): Promise<Penalty>;
  getDriverPenalties(driverId: string): Promise<Penalty[]>;

  // Statistics
  getDashboardStats(): Promise<{
    todayRides: number;
    activeDrivers: number;
    todayRevenue: number;
    todayCommission: number;
  }>;
}

export class DatabaseStorage implements IStorage {
  async getDriver(id: string): Promise<Driver | undefined> {
    const [driver] = await db.select().from(drivers).where(eq(drivers.id, id));
    return driver || undefined;
  }

  async getDriverByPhone(phone: string): Promise<Driver | undefined> {
    const [driver] = await db.select().from(drivers).where(eq(drivers.phone, phone));
    return driver || undefined;
  }

  async createDriver(insertDriver: InsertDriver): Promise<Driver> {
    const [driver] = await db
      .insert(drivers)
      .values(insertDriver)
      .returning();
    return driver;
  }

  async updateDriverStatus(id: string, status: 'active' | 'inactive' | 'busy'): Promise<void> {
    await db
      .update(drivers)
      .set({ status })
      .where(eq(drivers.id, id));
  }

  async updateDriverLocation(id: string, location: string): Promise<void> {
    await db
      .update(drivers)
      .set({ currentLocation: location })
      .where(eq(drivers.id, id));
  }

  async getAllDrivers(): Promise<DriverWithStats[]> {
    const result = await db
      .select({
        id: drivers.id,
        name: drivers.name,
        phone: drivers.phone,
        zalo: drivers.zalo,
        status: drivers.status,
        currentLocation: drivers.currentLocation,
        rating: drivers.rating,
        totalRides: drivers.totalRides,
        createdAt: drivers.createdAt,
        completedRides: count(rides.id),
      })
      .from(drivers)
      .leftJoin(rides, and(
        eq(rides.creatorId, drivers.id),
        eq(rides.status, 'completed')
      ))
      .groupBy(drivers.id);

    return result.map(row => ({
      ...row,
      rating: row.rating || '0.00',
      totalRides: row.totalRides || 0,
      completedRides: row.completedRides || 0,
    }));
  }

  async getRide(id: string): Promise<RideWithDrivers | undefined> {
    const [ride] = await db
      .select()
      .from(rides)
      .leftJoin(drivers, eq(rides.creatorId, drivers.id))
      .where(eq(rides.id, id));

    if (!ride) return undefined;

    let acceptor = undefined;
    if (ride.rides.acceptorId) {
      const [acceptorResult] = await db
        .select()
        .from(drivers)
        .where(eq(drivers.id, ride.rides.acceptorId));
      acceptor = acceptorResult;
    }

    return {
      ...ride.rides,
      creator: ride.drivers!,
      acceptor,
    };
  }

  async createRide(insertRide: InsertRide): Promise<Ride> {
    const commissionAmount = Math.floor((insertRide.price * insertRide.commissionRate) / 100);
    
    const [ride] = await db
      .insert(rides)
      .values({
        ...insertRide,
        commissionAmount,
      })
      .returning();
    return ride;
  }

  async getAvailableRides(driverLocation?: { province?: string; district?: string; commune?: string }): Promise<RideWithDrivers[]> {
    let query = db
      .select()
      .from(rides)
      .leftJoin(drivers, eq(rides.creatorId, drivers.id))
      .where(eq(rides.status, 'waiting'))
      .orderBy(desc(rides.createdAt));

    // Apply location-based ordering if provided
    if (driverLocation) {
      query = db
        .select()
        .from(rides)
        .leftJoin(drivers, eq(rides.creatorId, drivers.id))
        .where(eq(rides.status, 'waiting'))
        .orderBy(
          // Priority: same commune > same district > same province > others
          sql`
            CASE 
              WHEN ${rides.commune} = ${driverLocation.commune} THEN 1
              WHEN ${rides.district} = ${driverLocation.district} THEN 2
              WHEN ${rides.province} = ${driverLocation.province} THEN 3
              ELSE 4
            END,
            ${desc(rides.createdAt)}
          `
        );
    }

    const result = await query;

    return result.map(row => ({
      ...row.rides,
      creator: row.drivers!,
      acceptor: undefined,
    }));
  }

  async acceptRide(rideId: string, driverId: string): Promise<void> {
    await db
      .update(rides)
      .set({ 
        acceptorId: driverId, 
        status: 'accepted',
        updatedAt: new Date()
      })
      .where(eq(rides.id, rideId));

    await db
      .update(drivers)
      .set({ status: 'busy' })
      .where(eq(drivers.id, driverId));
  }

  async updateRideStatus(id: string, status: 'waiting' | 'accepted' | 'in_progress' | 'completed' | 'cancelled'): Promise<void> {
    await db
      .update(rides)
      .set({ status, updatedAt: new Date() })
      .where(eq(rides.id, id));

    // If ride is completed or cancelled, update driver status back to active
    if (status === 'completed' || status === 'cancelled') {
      const [ride] = await db.select().from(rides).where(eq(rides.id, id));
      if (ride?.acceptorId) {
        await db
          .update(drivers)
          .set({ status: 'active' })
          .where(eq(drivers.id, ride.acceptorId));
      }
    }
  }

  async getRideHistory(driverId: string): Promise<RideWithDrivers[]> {
    const result = await db
      .select()
      .from(rides)
      .leftJoin(drivers, eq(rides.creatorId, drivers.id))
      .where(
        or(
          eq(rides.creatorId, driverId),
          eq(rides.acceptorId, driverId)
        )
      )
      .orderBy(desc(rides.createdAt));

    const ridesWithAcceptors = await Promise.all(
      result.map(async (row) => {
        let acceptor = undefined;
        if (row.rides.acceptorId) {
          const [acceptorResult] = await db
            .select()
            .from(drivers)
            .where(eq(drivers.id, row.rides.acceptorId));
          acceptor = acceptorResult;
        }

        return {
          ...row.rides,
          creator: row.drivers!,
          acceptor,
        };
      })
    );

    return ridesWithAcceptors;
  }

  async getAllRides(): Promise<RideWithDrivers[]> {
    const result = await db
      .select()
      .from(rides)
      .leftJoin(drivers, eq(rides.creatorId, drivers.id))
      .orderBy(desc(rides.createdAt));

    const ridesWithAcceptors = await Promise.all(
      result.map(async (row) => {
        let acceptor = undefined;
        if (row.rides.acceptorId) {
          const [acceptorResult] = await db
            .select()
            .from(drivers)
            .where(eq(drivers.id, row.rides.acceptorId));
          acceptor = acceptorResult;
        }

        return {
          ...row.rides,
          creator: row.drivers!,
          acceptor,
        };
      })
    );

    return ridesWithAcceptors;
  }

  async getSystemConfig(): Promise<SystemConfig> {
    const [config] = await db.select().from(systemConfig).limit(1);
    
    if (!config) {
      // Create default config if none exists
      const [newConfig] = await db
        .insert(systemConfig)
        .values({})
        .returning();
      return newConfig;
    }
    
    return config;
  }

  async updateSystemConfig(configUpdate: Partial<InsertSystemConfig>): Promise<SystemConfig> {
    const currentConfig = await this.getSystemConfig();
    
    const [updatedConfig] = await db
      .update(systemConfig)
      .set({ ...configUpdate, updatedAt: new Date() })
      .where(eq(systemConfig.id, currentConfig.id))
      .returning();
    
    return updatedConfig;
  }

  async createPenalty(penalty: InsertPenalty): Promise<Penalty> {
    const [newPenalty] = await db
      .insert(penalties)
      .values(penalty)
      .returning();
    return newPenalty;
  }

  async getDriverPenalties(driverId: string): Promise<Penalty[]> {
    return await db
      .select()
      .from(penalties)
      .where(eq(penalties.driverId, driverId))
      .orderBy(desc(penalties.createdAt));
  }

  async getDashboardStats(): Promise<{
    todayRides: number;
    activeDrivers: number;
    todayRevenue: number;
    todayCommission: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [todayRidesResult] = await db
      .select({ count: count() })
      .from(rides)
      .where(
        and(
          sql`${rides.createdAt} >= ${today}`,
          sql`${rides.createdAt} < ${tomorrow}`
        )
      );

    const [activeDriversResult] = await db
      .select({ count: count() })
      .from(drivers)
      .where(eq(drivers.status, 'active'));

    const [revenueResult] = await db
      .select({ 
        totalRevenue: sql<number>`COALESCE(SUM(${rides.price}), 0)`,
        totalCommission: sql<number>`COALESCE(SUM(${rides.commissionAmount}), 0)`
      })
      .from(rides)
      .where(
        and(
          sql`${rides.createdAt} >= ${today}`,
          sql`${rides.createdAt} < ${tomorrow}`,
          eq(rides.status, 'completed')
        )
      );

    return {
      todayRides: todayRidesResult.count,
      activeDrivers: activeDriversResult.count,
      todayRevenue: revenueResult?.totalRevenue || 0,
      todayCommission: revenueResult?.totalCommission || 0,
    };
  }
}

export const storage = new DatabaseStorage();
