import '../../core/constants/api_constants.dart';
import '../models/ride_model.dart';
import 'api_client.dart';

class LocationFilterData {
  const LocationFilterData({
    this.province,
    this.district,
    this.commune,
  });

  final String? province;
  final String? district;
  final String? commune;

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{};
    if (province != null) params['province'] = province;
    if (district != null) params['district'] = district;
    if (commune != null) params['commune'] = commune;
    return params;
  }
}

abstract class RideRemoteDataSource {
  Future<List<RideWithDriversModel>> getAllRides();
  Future<List<RideWithDriversModel>> getAvailableRides({LocationFilterData? location});
  Future<List<RideWithDriversModel>> getRideHistory(String driverId);
  Future<RideWithDriversModel> getRide(String id);
  Future<RideWithDriversModel> createRide(CreateRideModel ride);
  Future<void> acceptRide(String rideId, String driverId);
  Future<void> updateRideStatus(String rideId, String status);
}

class RideRemoteDataSourceImpl implements RideRemoteDataSource {
  final ApiClient _apiClient;

  RideRemoteDataSourceImpl(this._apiClient);

  @override
  Future<List<RideWithDriversModel>> getAllRides() async {
    final response = await _apiClient.get(ApiConstants.rides);
    final List<dynamic> data = response.data as List<dynamic>;
    
    return data
        .map((json) => RideWithDriversModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<List<RideWithDriversModel>> getAvailableRides({LocationFilterData? location}) async {
    final response = await _apiClient.get(
      ApiConstants.ridesAvailable,
      queryParameters: location?.toQueryParameters(),
    );
    final List<dynamic> data = response.data as List<dynamic>;
    
    return data
        .map((json) => RideWithDriversModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<List<RideWithDriversModel>> getRideHistory(String driverId) async {
    final response = await _apiClient.get(ApiConstants.rideHistory(driverId));
    final List<dynamic> data = response.data as List<dynamic>;
    
    return data
        .map((json) => RideWithDriversModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<RideWithDriversModel> getRide(String id) async {
    final response = await _apiClient.get('${ApiConstants.rides}/$id');
    return RideWithDriversModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<RideWithDriversModel> createRide(CreateRideModel ride) async {
    final response = await _apiClient.post(
      ApiConstants.rides,
      data: ride.toJson(),
    );
    return RideWithDriversModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<void> acceptRide(String rideId, String driverId) async {
    await _apiClient.post(
      ApiConstants.acceptRide(rideId),
      data: {'driverId': driverId},
    );
  }

  @override
  Future<void> updateRideStatus(String rideId, String status) async {
    await _apiClient.patch(
      ApiConstants.updateRideStatus(rideId),
      data: {'status': status},
    );
  }
}
