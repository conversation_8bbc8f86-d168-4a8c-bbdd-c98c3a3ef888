// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverModelImpl _$$DriverModelImplFromJson(Map<String, dynamic> json) =>
    _$DriverModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String,
      zalo: json['zalo'] as String?,
      status:
          $enumDecodeNullable(_$DriverStatusEnumMap, json['status']) ??
          DriverStatus.active,
      currentLocation: json['currentLocation'] as String?,
      rating: json['rating'] as String? ?? '0.00',
      totalRides: (json['totalRides'] as num?)?.toInt() ?? 0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$DriverModelImplToJson(_$DriverModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'zalo': instance.zalo,
      'status': _$DriverStatusEnumMap[instance.status]!,
      'currentLocation': instance.currentLocation,
      'rating': instance.rating,
      'totalRides': instance.totalRides,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

const _$DriverStatusEnumMap = {
  DriverStatus.active: 'active',
  DriverStatus.inactive: 'inactive',
  DriverStatus.busy: 'busy',
};

_$DriverWithStatsModelImpl _$$DriverWithStatsModelImplFromJson(
  Map<String, dynamic> json,
) => _$DriverWithStatsModelImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  phone: json['phone'] as String,
  zalo: json['zalo'] as String?,
  status:
      $enumDecodeNullable(_$DriverStatusEnumMap, json['status']) ??
      DriverStatus.active,
  currentLocation: json['currentLocation'] as String?,
  rating: json['rating'] as String? ?? '0.00',
  totalRides: (json['totalRides'] as num?)?.toInt() ?? 0,
  completedRides: (json['completedRides'] as num?)?.toInt() ?? 0,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$$DriverWithStatsModelImplToJson(
  _$DriverWithStatsModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'phone': instance.phone,
  'zalo': instance.zalo,
  'status': _$DriverStatusEnumMap[instance.status]!,
  'currentLocation': instance.currentLocation,
  'rating': instance.rating,
  'totalRides': instance.totalRides,
  'completedRides': instance.completedRides,
  'createdAt': instance.createdAt?.toIso8601String(),
};

_$CreateDriverModelImpl _$$CreateDriverModelImplFromJson(
  Map<String, dynamic> json,
) => _$CreateDriverModelImpl(
  name: json['name'] as String,
  phone: json['phone'] as String,
  zalo: json['zalo'] as String?,
  status:
      $enumDecodeNullable(_$DriverStatusEnumMap, json['status']) ??
      DriverStatus.active,
  currentLocation: json['currentLocation'] as String?,
);

Map<String, dynamic> _$$CreateDriverModelImplToJson(
  _$CreateDriverModelImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'phone': instance.phone,
  'zalo': instance.zalo,
  'status': _$DriverStatusEnumMap[instance.status]!,
  'currentLocation': instance.currentLocation,
};
