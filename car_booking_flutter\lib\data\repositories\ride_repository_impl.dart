import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../../domain/entities/ride.dart';
import '../../domain/repositories/ride_repository.dart';
import '../datasources/ride_remote_data_source.dart';
import '../datasources/local_storage_service.dart';
import '../models/ride_model.dart';

class RideRepositoryImpl implements RideRepository {
  final RideRemoteDataSource _remoteDataSource;
  final LocalStorageService _localStorage;

  RideRepositoryImpl(this._remoteDataSource, this._localStorage);

  LocationFilterData? _convertLocationFilter(LocationFilter? filter) {
    if (filter == null) return null;
    return LocationFilterData(
      province: filter.province,
      district: filter.district,
      commune: filter.commune,
    );
  }

  @override
  Future<Either<Failure, List<RideWithDrivers>>> getAllRides() async {
    try {
      // Try to get from cache first
      const cacheKey = 'all_rides';
      const maxAge = Duration(minutes: 2);
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final List<dynamic> ridesJson = cachedData['rides'] as List<dynamic>;
          final rides = ridesJson
              .map((json) => RideWithDriversModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();
          return Right(rides);
        }
      }

      // Fetch from remote
      final remoteRides = await _remoteDataSource.getAllRides();
      final rides = remoteRides.map((model) => model.toEntity()).toList();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'rides': remoteRides.map((model) => model.toJson()).toList(),
      });

      return Right(rides);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<RideWithDrivers>>> getAvailableRides({LocationFilter? location}) async {
    try {
      // Create cache key based on location filter
      final locationKey = location != null 
          ? '${location.province}_${location.district}_${location.commune}'
          : 'all';
      final cacheKey = 'available_rides_$locationKey';
      const maxAge = Duration(seconds: 30); // Short cache for available rides
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final List<dynamic> ridesJson = cachedData['rides'] as List<dynamic>;
          final rides = ridesJson
              .map((json) => RideWithDriversModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();
          return Right(rides);
        }
      }

      // Fetch from remote
      final locationFilter = _convertLocationFilter(location);
      final remoteRides = await _remoteDataSource.getAvailableRides(location: locationFilter);
      final rides = remoteRides.map((model) => model.toEntity()).toList();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'rides': remoteRides.map((model) => model.toJson()).toList(),
      });

      return Right(rides);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<RideWithDrivers>>> getRideHistory(String driverId) async {
    try {
      final cacheKey = 'ride_history_$driverId';
      const maxAge = Duration(minutes: 5);
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final List<dynamic> ridesJson = cachedData['rides'] as List<dynamic>;
          final rides = ridesJson
              .map((json) => RideWithDriversModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();
          return Right(rides);
        }
      }

      // Fetch from remote
      final remoteRides = await _remoteDataSource.getRideHistory(driverId);
      final rides = remoteRides.map((model) => model.toEntity()).toList();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'rides': remoteRides.map((model) => model.toJson()).toList(),
      });

      return Right(rides);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, RideWithDrivers>> getRide(String id) async {
    try {
      final remoteRide = await _remoteDataSource.getRide(id);
      return Right(remoteRide.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, RideWithDrivers>> createRide(CreateRide ride) async {
    try {
      final createRideModel = CreateRideModel(
        creatorId: ride.creatorId,
        vehicleType: ride.vehicleType,
        price: ride.price,
        commissionRate: ride.commissionRate,
        pickupTime: ride.pickupTime,
        pickupAddress: ride.pickupAddress,
        passengerZalo: ride.passengerZalo,
        isAsap: ride.isAsap,
        province: ride.province,
        district: ride.district,
        commune: ride.commune,
      );
      
      final remoteRide = await _remoteDataSource.createRide(createRideModel);
      
      // Invalidate relevant caches
      await _localStorage.removeCachedData('all_rides');
      // Clear all available rides caches
      final cacheKeys = ['available_rides_all'];
      for (final key in cacheKeys) {
        await _localStorage.removeCachedData(key);
      }
      
      return Right(remoteRide.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> acceptRide(String rideId, String driverId) async {
    try {
      await _remoteDataSource.acceptRide(rideId, driverId);
      
      // Invalidate relevant caches
      await _localStorage.removeCachedData('all_rides');
      await _localStorage.removeCachedData('available_rides_all');
      await _localStorage.removeCachedData('ride_history_$driverId');
      
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateRideStatus(String rideId, String status) async {
    try {
      await _remoteDataSource.updateRideStatus(rideId, status);
      
      // Invalidate relevant caches
      await _localStorage.removeCachedData('all_rides');
      await _localStorage.removeCachedData('available_rides_all');
      
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }
}
