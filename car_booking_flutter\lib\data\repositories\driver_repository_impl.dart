import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../../domain/entities/driver.dart';
import '../../domain/repositories/driver_repository.dart';
import '../datasources/driver_remote_data_source.dart';
import '../datasources/local_storage_service.dart';
import '../models/driver_model.dart';

class DriverRepositoryImpl implements DriverRepository {
  final DriverRemoteDataSource _remoteDataSource;
  final LocalStorageService _localStorage;

  DriverRepositoryImpl(this._remoteDataSource, this._localStorage);

  @override
  Future<Either<Failure, List<DriverWithStats>>> getAllDrivers() async {
    try {
      // Try to get from cache first
      const cacheKey = 'drivers_list';
      const maxAge = Duration(minutes: 5);
      
      if (_localStorage.isCacheValid(cacheKey, maxAge)) {
        final cachedData = _localStorage.getCachedData(cacheKey);
        if (cachedData != null) {
          final List<dynamic> driversJson = cachedData['drivers'] as List<dynamic>;
          final drivers = driversJson
              .map((json) => DriverWithStatsModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();
          return Right(drivers);
        }
      }

      // Fetch from remote
      final remoteDrivers = await _remoteDataSource.getAllDrivers();
      final drivers = remoteDrivers.map((model) => model.toEntity()).toList();

      // Cache the result
      await _localStorage.cacheDataWithTimestamp(cacheKey, {
        'drivers': remoteDrivers.map((model) => model.toJson()).toList(),
      });

      return Right(drivers);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Driver>> getDriver(String id) async {
    try {
      final remoteDriver = await _remoteDataSource.getDriver(id);
      return Right(remoteDriver.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Driver>> getDriverByPhone(String phone) async {
    try {
      final remoteDriver = await _remoteDataSource.getDriverByPhone(phone);
      return Right(remoteDriver.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Driver>> createDriver(CreateDriver driver) async {
    try {
      final createDriverModel = CreateDriverModel(
        name: driver.name,
        phone: driver.phone,
        zalo: driver.zalo,
        status: driver.status,
        currentLocation: driver.currentLocation,
      );
      
      final remoteDriver = await _remoteDataSource.createDriver(createDriverModel);
      
      // Invalidate drivers cache
      await _localStorage.removeCachedData('drivers_list');
      
      return Right(remoteDriver.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateDriverStatus(String id, String status) async {
    try {
      await _remoteDataSource.updateDriverStatus(id, status);
      
      // Invalidate drivers cache
      await _localStorage.removeCachedData('drivers_list');
      
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateDriverLocation(String id, String location) async {
    try {
      await _remoteDataSource.updateDriverLocation(id, location);
      
      // Invalidate drivers cache
      await _localStorage.removeCachedData('drivers_list');
      
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }
}
