import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/dashboard_stats.dart';

part 'dashboard_stats_model.freezed.dart';
part 'dashboard_stats_model.g.dart';

@freezed
class DashboardStatsModel with _$DashboardStatsModel {
  const factory DashboardStatsModel({
    @Default(0) int todayRides,
    @Default(0) int activeDrivers,
    @Default(0) int todayRevenue,
    @Default(0) int todayCommission,
  }) = _DashboardStatsModel;

  factory DashboardStatsModel.fromJson(Map<String, dynamic> json) => _$DashboardStatsModelFromJson(json);
}

// Extension to convert between model and entity
extension DashboardStatsModelX on DashboardStatsModel {
  DashboardStats toEntity() {
    return DashboardStats(
      todayRides: todayRides,
      activeDrivers: activeDrivers,
      todayRevenue: todayRevenue,
      todayCommission: todayCommission,
    );
  }
}
