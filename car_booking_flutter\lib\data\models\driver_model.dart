import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:equatable/equatable.dart';
import '../../core/constants/enums.dart';
import '../../domain/entities/driver.dart';

part 'driver_model.freezed.dart';
part 'driver_model.g.dart';

@freezed
class DriverModel with _$DriverModel {
  const factory DriverModel({
    required String id,
    required String name,
    required String phone,
    String? zalo,
    @Default(DriverStatus.active) DriverStatus status,
    String? currentLocation,
    @Default('0.00') String rating,
    @Default(0) int totalRides,
    DateTime? createdAt,
  }) = _DriverModel;

  factory DriverModel.fromJson(Map<String, dynamic> json) => _$DriverModelFromJson(json);
}

@freezed
class DriverWithStatsModel with _$DriverWithStatsModel {
  const factory DriverWithStatsModel({
    required String id,
    required String name,
    required String phone,
    String? zalo,
    @Default(DriverStatus.active) DriverStatus status,
    String? currentLocation,
    @Default('0.00') String rating,
    @Default(0) int totalRides,
    @Default(0) int completedRides,
    DateTime? createdAt,
  }) = _DriverWithStatsModel;

  factory DriverWithStatsModel.fromJson(Map<String, dynamic> json) => _$DriverWithStatsModelFromJson(json);
}

@freezed
class CreateDriverModel with _$CreateDriverModel {
  const factory CreateDriverModel({
    required String name,
    required String phone,
    String? zalo,
    @Default(DriverStatus.active) DriverStatus status,
    String? currentLocation,
  }) = _CreateDriverModel;

  factory CreateDriverModel.fromJson(Map<String, dynamic> json) => _$CreateDriverModelFromJson(json);
}

// Extensions to convert between models and entities
extension DriverModelX on DriverModel {
  Driver toEntity() {
    return Driver(
      id: id,
      name: name,
      phone: phone,
      zalo: zalo,
      status: status,
      currentLocation: currentLocation,
      rating: rating,
      totalRides: totalRides,
      createdAt: createdAt,
    );
  }
}

extension DriverWithStatsModelX on DriverWithStatsModel {
  DriverWithStats toEntity() {
    return DriverWithStats(
      id: id,
      name: name,
      phone: phone,
      zalo: zalo,
      status: status,
      currentLocation: currentLocation,
      rating: rating,
      totalRides: totalRides,
      completedRides: completedRides,
      createdAt: createdAt,
    );
  }
}

extension CreateDriverModelX on CreateDriverModel {
  CreateDriver toEntity() {
    return CreateDriver(
      name: name,
      phone: phone,
      zalo: zalo,
      status: status,
      currentLocation: currentLocation,
    );
  }
}
