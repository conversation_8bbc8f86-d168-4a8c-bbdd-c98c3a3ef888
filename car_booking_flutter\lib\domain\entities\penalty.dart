import 'package:equatable/equatable.dart';
import '../../core/constants/enums.dart';

class Penalty extends Equatable {
  const Penalty({
    required this.id,
    required this.driverId,
    this.rideId,
    required this.type,
    required this.amount,
    this.reason,
    this.createdAt,
  });

  final String id;
  final String driverId;
  final String? rideId;
  final PenaltyType type;
  final int amount;
  final String? reason;
  final DateTime? createdAt;

  @override
  List<Object?> get props => [
        id,
        driverId,
        rideId,
        type,
        amount,
        reason,
        createdAt,
      ];

  Penalty copyWith({
    String? id,
    String? driverId,
    String? rideId,
    PenaltyType? type,
    int? amount,
    String? reason,
    DateTime? createdAt,
  }) {
    return Penalty(
      id: id ?? this.id,
      driverId: driverId ?? this.driverId,
      rideId: rideId ?? this.rideId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      reason: reason ?? this.reason,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class CreatePenalty extends Equatable {
  const CreatePenalty({
    required this.driverId,
    this.rideId,
    required this.type,
    required this.amount,
    this.reason,
  });

  final String driverId;
  final String? rideId;
  final PenaltyType type;
  final int amount;
  final String? reason;

  @override
  List<Object?> get props => [
        driverId,
        rideId,
        type,
        amount,
        reason,
      ];
}
