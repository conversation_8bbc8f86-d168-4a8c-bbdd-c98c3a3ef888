# Overview

This is an internal ride-hailing system designed for Vietnamese taxi companies, featuring a dual-role platform for drivers and administrators. Drivers can create and accept ride requests within their organization, while admins manage the entire fleet, configure system settings, and monitor operations through comprehensive dashboards.

The system emphasizes mobile-first design for drivers on-the-go, real-time updates via WebSocket connections, and flexible commission/penalty management for business operations.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
- **React 18** with TypeScript for type safety
- **Vite** as the build tool and development server
- **Tailwind CSS** with shadcn/ui component library for consistent styling
- **Wouter** for client-side routing (lightweight React Router alternative)
- **TanStack React Query** for server state management and caching
- **React Hook Form** with Zod validation for form handling
- **Mobile-first responsive design** optimized for driver usage

## Backend Architecture
- **Express.js** server with TypeScript
- **WebSocket integration** using native WebSocket API for real-time updates
- **RESTful API** design with proper error handling and logging middleware
- **Modular route structure** separating concerns for drivers, rides, and admin functions
- **Storage abstraction layer** providing clean interface for database operations

## Data Layer
- **Drizzle ORM** with PostgreSQL dialect for type-safe database interactions
- **Neon Database** as the PostgreSQL hosting solution
- **Schema-driven development** with shared types between frontend and backend
- **Database migrations** managed through Drizzle Kit

## Real-time Communication
- **WebSocket server** integrated with HTTP server for live ride updates
- **Auto-refresh mechanism** with 5-second intervals as fallback
- **Event-driven updates** for ride status changes, driver assignments, and system notifications

## State Management Strategy
- **Server state** handled by React Query with aggressive caching
- **Local component state** using React hooks
- **Form state** managed by React Hook Form with validation
- **Real-time synchronization** between multiple client connections

## Mobile Optimization
- **Progressive Web App** capabilities with offline-first considerations
- **Touch-optimized interfaces** for driver dashboard usage while driving
- **Responsive breakpoints** prioritizing mobile experience
- **Performance optimization** with code splitting and lazy loading

# External Dependencies

## Database & Hosting
- **@neondatabase/serverless** - PostgreSQL database hosting with serverless architecture
- **Neon Database** - Managed PostgreSQL service with automatic scaling

## UI Framework & Styling
- **@radix-ui/* components** - Headless UI primitives for accessibility
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Pre-built component library built on Radix UI
- **Lucide React** - Icon library for consistent iconography

## Development Tools
- **Vite** - Build tool and development server
- **TypeScript** - Type safety and developer experience
- **ESBuild** - Fast JavaScript bundler for production builds
- **PostCSS & Autoprefixer** - CSS processing and vendor prefixing

## Form & Validation
- **React Hook Form** - Performance-focused form library
- **@hookform/resolvers** - Validation resolver for external schema libraries
- **Zod** - TypeScript-first schema validation
- **drizzle-zod** - Integration between Drizzle ORM and Zod schemas

## State Management
- **@tanstack/react-query** - Server state management with caching
- **Native WebSocket** - Real-time bidirectional communication

## Geographic Services
- **Browser Geolocation API** - For driver location tracking
- **Mock geocoding service** - Placeholder for future Google Maps integration
- **Location-based filtering** - Ride matching by province/district/commune