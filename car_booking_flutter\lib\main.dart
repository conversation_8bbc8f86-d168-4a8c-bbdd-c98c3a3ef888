import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'core/di/injection_container.dart' as di;
import 'core/theme/app_theme.dart';
import 'presentation/blocs/app/app_cubit.dart';
import 'presentation/pages/app_wrapper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  await di.init();

  runApp(const CarBookingApp());
}

class CarBookingApp extends StatelessWidget {
  const CarBookingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return BlocProvider(
          create: (context) => di.sl<AppCubit>()..initialize(),
          child: MaterialApp(
            title: '<PERSON><PERSON> Thống Đặt Xe',
            theme: AppTheme.lightTheme,
            debugShowCheckedModeBanner: false,
            home: const AppWrapper(),
          ),
        );
      },
    );
  }
}


