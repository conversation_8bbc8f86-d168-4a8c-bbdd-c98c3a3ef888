import { sql } from "drizzle-orm";
import { pgTable, text, varchar, integer, timestamp, decimal, boolean, pgEnum } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Enums
export const rideStatusEnum = pgEnum('ride_status', ['waiting', 'accepted', 'in_progress', 'completed', 'cancelled']);
export const vehicleTypeEnum = pgEnum('vehicle_type', ['4_seats', '7_seats', '9_seats', '16_seats']);
export const driverStatusEnum = pgEnum('driver_status', ['active', 'inactive', 'busy']);

// Tables
export const drivers = pgTable("drivers", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  phone: text("phone").notNull().unique(),
  zalo: text("zalo"),
  status: driverStatusEnum("status").default('active'),
  currentLocation: text("current_location"),
  rating: decimal("rating", { precision: 3, scale: 2 }).default('0.00'),
  totalRides: integer("total_rides").default(0),
  createdAt: timestamp("created_at").defaultNow(),
});

export const rides = pgTable("rides", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  creatorId: varchar("creator_id").notNull().references(() => drivers.id),
  acceptorId: varchar("acceptor_id").references(() => drivers.id),
  vehicleType: vehicleTypeEnum("vehicle_type").notNull(),
  price: integer("price").notNull(), // in VND
  commissionRate: integer("commission_rate").notNull(), // percentage
  commissionAmount: integer("commission_amount").notNull(),
  pickupTime: timestamp("pickup_time").notNull(),
  pickupAddress: text("pickup_address").notNull(),
  passengerZalo: text("passenger_zalo").notNull(),
  status: rideStatusEnum("status").default('waiting'),
  isAsap: boolean("is_asap").default(false),
  province: text("province"),
  district: text("district"),
  commune: text("commune"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const systemConfig = pgTable("system_config", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  defaultCommissionRate: integer("default_commission_rate").default(10),
  driverSharePercentage: integer("driver_share_percentage").default(70),
  cancelPenalty: integer("cancel_penalty").default(25000),
  noPickupPenalty: integer("no_pickup_penalty").default(50000),
  missedPickupPenalty: integer("missed_pickup_penalty").default(75000),
  warningTimeMinutes: integer("warning_time_minutes").default(15),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const penalties = pgTable("penalties", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  driverId: varchar("driver_id").notNull().references(() => drivers.id),
  rideId: varchar("ride_id").references(() => rides.id),
  type: text("type").notNull(), // 'cancel', 'no_pickup', 'missed_pickup'
  amount: integer("amount").notNull(),
  reason: text("reason"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const driversRelations = relations(drivers, ({ many }) => ({
  createdRides: many(rides, { relationName: "creator" }),
  acceptedRides: many(rides, { relationName: "acceptor" }),
  penalties: many(penalties),
}));

export const ridesRelations = relations(rides, ({ one }) => ({
  creator: one(drivers, {
    fields: [rides.creatorId],
    references: [drivers.id],
    relationName: "creator",
  }),
  acceptor: one(drivers, {
    fields: [rides.acceptorId],
    references: [drivers.id],
    relationName: "acceptor",
  }),
}));

export const penaltiesRelations = relations(penalties, ({ one }) => ({
  driver: one(drivers, {
    fields: [penalties.driverId],
    references: [drivers.id],
  }),
  ride: one(rides, {
    fields: [penalties.rideId],
    references: [rides.id],
  }),
}));

// Schemas
export const insertDriverSchema = createInsertSchema(drivers).omit({
  id: true,
  createdAt: true,
  totalRides: true,
  rating: true,
});

export const insertRideSchema = createInsertSchema(rides).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  acceptorId: true,
  status: true,
  commissionAmount: true,
});

export const insertSystemConfigSchema = createInsertSchema(systemConfig).omit({
  id: true,
  updatedAt: true,
});

export const insertPenaltySchema = createInsertSchema(penalties).omit({
  id: true,
  createdAt: true,
});

// Types
export type Driver = typeof drivers.$inferSelect;
export type InsertDriver = z.infer<typeof insertDriverSchema>;
export type Ride = typeof rides.$inferSelect;
export type InsertRide = z.infer<typeof insertRideSchema>;
export type SystemConfig = typeof systemConfig.$inferSelect;
export type InsertSystemConfig = z.infer<typeof insertSystemConfigSchema>;
export type Penalty = typeof penalties.$inferSelect;
export type InsertPenalty = z.infer<typeof insertPenaltySchema>;

// Extended types with relations
export type RideWithDrivers = Ride & {
  creator: Driver;
  acceptor?: Driver;
};

export type DriverWithStats = Driver & {
  totalRides: number;
  completedRides: number;
  rating: string;
};
