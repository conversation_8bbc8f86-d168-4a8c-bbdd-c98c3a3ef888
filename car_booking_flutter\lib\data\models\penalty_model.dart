import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/constants/enums.dart';
import '../../domain/entities/penalty.dart';

part 'penalty_model.freezed.dart';
part 'penalty_model.g.dart';

@freezed
class PenaltyModel with _$PenaltyModel {
  const factory PenaltyModel({
    required String id,
    required String driverId,
    String? rideId,
    required PenaltyType type,
    required int amount,
    String? reason,
    DateTime? createdAt,
  }) = _PenaltyModel;

  factory PenaltyModel.fromJson(Map<String, dynamic> json) => _$PenaltyModelFromJson(json);
}

@freezed
class CreatePenaltyModel with _$CreatePenaltyModel {
  const factory CreatePenaltyModel({
    required String driverId,
    String? rideId,
    required PenaltyType type,
    required int amount,
    String? reason,
  }) = _CreatePenaltyModel;

  factory CreatePenaltyModel.fromJson(Map<String, dynamic> json) => _$CreatePenaltyModelFromJson(json);
}

// Extensions to convert between models and entities
extension PenaltyModelX on PenaltyModel {
  Penalty toEntity() {
    return Penalty(
      id: id,
      driverId: driverId,
      rideId: rideId,
      type: type,
      amount: amount,
      reason: reason,
      createdAt: createdAt,
    );
  }
}

extension CreatePenaltyModelX on CreatePenaltyModel {
  CreatePenalty toEntity() {
    return CreatePenalty(
      driverId: driverId,
      rideId: rideId,
      type: type,
      amount: amount,
      reason: reason,
    );
  }
}
