// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DashboardStatsModelImpl _$$DashboardStatsModelImplFromJson(
  Map<String, dynamic> json,
) => _$DashboardStatsModelImpl(
  todayRides: (json['todayRides'] as num?)?.toInt() ?? 0,
  activeDrivers: (json['activeDrivers'] as num?)?.toInt() ?? 0,
  todayRevenue: (json['todayRevenue'] as num?)?.toInt() ?? 0,
  todayCommission: (json['todayCommission'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$$DashboardStatsModelImplToJson(
  _$DashboardStatsModelImpl instance,
) => <String, dynamic>{
  'todayRides': instance.todayRides,
  'activeDrivers': instance.activeDrivers,
  'todayRevenue': instance.todayRevenue,
  'todayCommission': instance.todayCommission,
};
