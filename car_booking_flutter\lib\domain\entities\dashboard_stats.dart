import 'package:equatable/equatable.dart';

class DashboardStats extends Equatable {
  const DashboardStats({
    this.todayRides = 0,
    this.activeDrivers = 0,
    this.todayRevenue = 0,
    this.todayCommission = 0,
  });

  final int todayRides;
  final int activeDrivers;
  final int todayRevenue;
  final int todayCommission;

  @override
  List<Object?> get props => [
        todayRides,
        activeDrivers,
        todayRevenue,
        todayCommission,
      ];

  DashboardStats copyWith({
    int? todayRides,
    int? activeDrivers,
    int? todayRevenue,
    int? todayCommission,
  }) {
    return DashboardStats(
      todayRides: todayRides ?? this.todayRides,
      activeDrivers: activeDrivers ?? this.activeDrivers,
      todayRevenue: todayRevenue ?? this.todayRevenue,
      todayCommission: todayCommission ?? this.todayCommission,
    );
  }
}
