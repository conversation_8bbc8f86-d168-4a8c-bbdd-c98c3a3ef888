import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../core/errors/exceptions.dart';

class LocalStorageService {
  static const String _userRoleKey = 'user_role';
  static const String _currentDriverIdKey = 'current_driver_id';
  static const String _settingsKey = 'app_settings';
  static const String _cacheBoxName = 'cache_box';
  
  late final SharedPreferences _prefs;
  late final Box _cacheBox;
  
  static LocalStorageService? _instance;
  static LocalStorageService get instance => _instance!;
  
  static Future<void> init() async {
    if (_instance != null) return;
    
    _instance = LocalStorageService._();
    await _instance!._init();
  }
  
  LocalStorageService._();
  
  Future<void> _init() async {
    try {
      await Hive.initFlutter();
      _cacheBox = await Hive.openBox(_cacheBoxName);
      _prefs = await SharedPreferences.getInstance();
    } catch (e) {
      throw CacheException('Failed to initialize local storage: $e');
    }
  }

  // User preferences
  Future<void> setUserRole(String role) async {
    try {
      await _prefs.setString(_userRoleKey, role);
    } catch (e) {
      throw CacheException('Failed to save user role: $e');
    }
  }

  String? getUserRole() {
    try {
      return _prefs.getString(_userRoleKey);
    } catch (e) {
      throw CacheException('Failed to get user role: $e');
    }
  }

  Future<void> setCurrentDriverId(String driverId) async {
    try {
      await _prefs.setString(_currentDriverIdKey, driverId);
    } catch (e) {
      throw CacheException('Failed to save current driver ID: $e');
    }
  }

  String? getCurrentDriverId() {
    try {
      return _prefs.getString(_currentDriverIdKey);
    } catch (e) {
      throw CacheException('Failed to get current driver ID: $e');
    }
  }

  // App settings
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    try {
      final settingsJson = jsonEncode(settings);
      await _prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      throw CacheException('Failed to save settings: $e');
    }
  }

  Map<String, dynamic>? getSettings() {
    try {
      final settingsJson = _prefs.getString(_settingsKey);
      if (settingsJson == null) return null;
      
      return jsonDecode(settingsJson) as Map<String, dynamic>;
    } catch (e) {
      throw CacheException('Failed to get settings: $e');
    }
  }

  // Cache operations
  Future<void> cacheData(String key, Map<String, dynamic> data) async {
    try {
      await _cacheBox.put(key, data);
    } catch (e) {
      throw CacheException('Failed to cache data: $e');
    }
  }

  Map<String, dynamic>? getCachedData(String key) {
    try {
      final data = _cacheBox.get(key);
      if (data == null) return null;
      
      return Map<String, dynamic>.from(data as Map);
    } catch (e) {
      throw CacheException('Failed to get cached data: $e');
    }
  }

  Future<void> removeCachedData(String key) async {
    try {
      await _cacheBox.delete(key);
    } catch (e) {
      throw CacheException('Failed to remove cached data: $e');
    }
  }

  Future<void> clearCache() async {
    try {
      await _cacheBox.clear();
    } catch (e) {
      throw CacheException('Failed to clear cache: $e');
    }
  }

  // Check if cached data is still valid
  bool isCacheValid(String key, Duration maxAge) {
    try {
      final data = _cacheBox.get(key);
      if (data == null) return false;
      
      final cachedAt = data['cached_at'] as int?;
      if (cachedAt == null) return false;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedAt);
      final now = DateTime.now();
      
      return now.difference(cacheTime) < maxAge;
    } catch (e) {
      return false;
    }
  }

  // Cache data with timestamp
  Future<void> cacheDataWithTimestamp(String key, Map<String, dynamic> data) async {
    try {
      final dataWithTimestamp = {
        ...data,
        'cached_at': DateTime.now().millisecondsSinceEpoch,
      };
      await _cacheBox.put(key, dataWithTimestamp);
    } catch (e) {
      throw CacheException('Failed to cache data with timestamp: $e');
    }
  }

  Future<void> clearAll() async {
    try {
      await _prefs.clear();
      await _cacheBox.clear();
    } catch (e) {
      throw CacheException('Failed to clear all data: $e');
    }
  }
}
