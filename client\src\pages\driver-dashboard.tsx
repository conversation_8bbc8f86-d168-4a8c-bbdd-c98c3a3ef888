import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Car, Plus, History } from "lucide-react";
import AvailableRides from "@/components/driver/available-rides";
import CreateRide from "@/components/driver/create-ride";
import RideHistory from "@/components/driver/ride-history";
import RefreshIndicator from "@/components/ui/refresh-indicator";
import { useWebSocket } from "@/hooks/use-websocket";

export default function DriverDashboard() {
  const [activeTab, setActiveTab] = useState<'available' | 'create' | 'history'>('available');
  const [refreshTimer, setRefreshTimer] = useState(5);
  const [isOnline, setIsOnline] = useState(true);
  const [currentDriver] = useState({ id: 'demo-driver-id', name: 'Demo Driver' }); // In real app, get from auth
  const queryClient = useQueryClient();

  // WebSocket for real-time updates  
  const socket = useWebSocket('/ws');

  // Monitor connection status
  useEffect(() => {
    const checkOnline = () => setIsOnline(navigator.onLine);
    
    window.addEventListener('online', checkOnline);
    window.addEventListener('offline', checkOnline);
    
    return () => {
      window.removeEventListener('online', checkOnline);
      window.removeEventListener('offline', checkOnline);
    };
  }, []);

  // Auto-refresh timer - only refresh data when online
  useEffect(() => {
    if (!isOnline) return;
    
    const interval = setInterval(() => {
      setRefreshTimer((prev) => {
        if (prev <= 1) {
          // Refresh only relevant data based on active tab
          if (activeTab === 'available') {
            queryClient.invalidateQueries({ queryKey: ['/api/rides/available'] });
          } else if (activeTab === 'history') {
            queryClient.invalidateQueries({ queryKey: ['/api/rides/history', currentDriver.id] });
          }
          return 5;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [queryClient, currentDriver.id, activeTab, isOnline]);

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Status Bar */}
      <div className="bg-primary text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm opacity-90">Trạng thái</p>
            <p className="font-semibold">Đang hoạt động</p>
          </div>
          <div className="text-right">
            <p className="text-sm opacity-90">Vị trí hiện tại</p>
            <p className="font-semibold text-sm">Quận 1, TP.HCM</p>
          </div>
        </div>
      </div>

      {/* Auto-refresh indicator */}
      <div className="px-4 py-2 border-b">
        <RefreshIndicator 
          countdown={refreshTimer}
          isOnline={isOnline}
          className="mx-auto w-fit"
        />
      </div>

      {/* Action Tabs */}
      <div className="flex bg-white border-b">
        <button
          onClick={() => setActiveTab('available')}
          className={`flex-1 py-4 px-4 text-center font-medium border-b-2 transition-colors ${
            activeTab === 'available'
              ? 'text-primary border-primary'
              : 'text-gray-500 border-transparent hover:text-gray-700'
          }`}
        >
          <Car className="w-4 h-4 mr-2 inline" />
          Cuốc Xe Khả Dụng
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 py-4 px-4 text-center font-medium border-b-2 transition-colors ${
            activeTab === 'create'
              ? 'text-primary border-primary'
              : 'text-gray-500 border-transparent hover:text-gray-700'
          }`}
        >
          <Plus className="w-4 h-4 mr-2 inline" />
          Tạo Cuốc
        </button>
        <button
          onClick={() => setActiveTab('history')}
          className={`flex-1 py-4 px-4 text-center font-medium border-b-2 transition-colors ${
            activeTab === 'history'
              ? 'text-primary border-primary'
              : 'text-gray-500 border-transparent hover:text-gray-700'
          }`}
        >
          <History className="w-4 h-4 mr-2 inline" />
          Lịch Sử
        </button>
      </div>

      {/* Tab Content */}
      <div className="min-h-[calc(100vh-200px)]">
        {activeTab === 'available' && <AvailableRides />}
        {activeTab === 'create' && <CreateRide />}
        {activeTab === 'history' && <RideHistory driverId={currentDriver.id} />}
      </div>
    </div>
  );
}
