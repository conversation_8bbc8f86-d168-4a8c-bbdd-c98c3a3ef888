// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'system_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SystemConfigModelImpl _$$SystemConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$SystemConfigModelImpl(
  id: json['id'] as String,
  defaultCommissionRate: (json['defaultCommissionRate'] as num?)?.toInt() ?? 10,
  driverSharePercentage: (json['driverSharePercentage'] as num?)?.toInt() ?? 70,
  cancelPenalty: (json['cancelPenalty'] as num?)?.toInt() ?? 25000,
  noPickupPenalty: (json['noPickupPenalty'] as num?)?.toInt() ?? 50000,
  missedPickupPenalty: (json['missedPickupPenalty'] as num?)?.toInt() ?? 75000,
  warningTimeMinutes: (json['warningTimeMinutes'] as num?)?.toInt() ?? 15,
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$SystemConfigModelImplToJson(
  _$SystemConfigModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'defaultCommissionRate': instance.defaultCommissionRate,
  'driverSharePercentage': instance.driverSharePercentage,
  'cancelPenalty': instance.cancelPenalty,
  'noPickupPenalty': instance.noPickupPenalty,
  'missedPickupPenalty': instance.missedPickupPenalty,
  'warningTimeMinutes': instance.warningTimeMinutes,
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

_$UpdateSystemConfigModelImpl _$$UpdateSystemConfigModelImplFromJson(
  Map<String, dynamic> json,
) => _$UpdateSystemConfigModelImpl(
  defaultCommissionRate: (json['defaultCommissionRate'] as num?)?.toInt(),
  driverSharePercentage: (json['driverSharePercentage'] as num?)?.toInt(),
  cancelPenalty: (json['cancelPenalty'] as num?)?.toInt(),
  noPickupPenalty: (json['noPickupPenalty'] as num?)?.toInt(),
  missedPickupPenalty: (json['missedPickupPenalty'] as num?)?.toInt(),
  warningTimeMinutes: (json['warningTimeMinutes'] as num?)?.toInt(),
);

Map<String, dynamic> _$$UpdateSystemConfigModelImplToJson(
  _$UpdateSystemConfigModelImpl instance,
) => <String, dynamic>{
  'defaultCommissionRate': instance.defaultCommissionRate,
  'driverSharePercentage': instance.driverSharePercentage,
  'cancelPenalty': instance.cancelPenalty,
  'noPickupPenalty': instance.noPickupPenalty,
  'missedPickupPenalty': instance.missedPickupPenalty,
  'warningTimeMinutes': instance.warningTimeMinutes,
};
