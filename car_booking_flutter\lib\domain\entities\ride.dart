import 'package:equatable/equatable.dart';
import '../../core/constants/enums.dart';
import 'driver.dart';

class Ride extends Equatable {
  const Ride({
    required this.id,
    required this.creatorId,
    this.acceptorId,
    required this.vehicleType,
    required this.price,
    required this.commissionRate,
    required this.commissionAmount,
    required this.pickupTime,
    required this.pickupAddress,
    required this.passengerZalo,
    this.status = RideStatus.waiting,
    this.isAsap = false,
    this.province,
    this.district,
    this.commune,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String creatorId;
  final String? acceptorId;
  final VehicleType vehicleType;
  final int price;
  final int commissionRate;
  final int commissionAmount;
  final DateTime pickupTime;
  final String pickupAddress;
  final String passengerZalo;
  final RideStatus status;
  final bool isAsap;
  final String? province;
  final String? district;
  final String? commune;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  @override
  List<Object?> get props => [
        id,
        creatorId,
        acceptorId,
        vehicleType,
        price,
        commissionRate,
        commissionAmount,
        pickupTime,
        pickupAddress,
        passengerZalo,
        status,
        isAsap,
        province,
        district,
        commune,
        createdAt,
        updatedAt,
      ];

  Ride copyWith({
    String? id,
    String? creatorId,
    String? acceptorId,
    VehicleType? vehicleType,
    int? price,
    int? commissionRate,
    int? commissionAmount,
    DateTime? pickupTime,
    String? pickupAddress,
    String? passengerZalo,
    RideStatus? status,
    bool? isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Ride(
      id: id ?? this.id,
      creatorId: creatorId ?? this.creatorId,
      acceptorId: acceptorId ?? this.acceptorId,
      vehicleType: vehicleType ?? this.vehicleType,
      price: price ?? this.price,
      commissionRate: commissionRate ?? this.commissionRate,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      pickupTime: pickupTime ?? this.pickupTime,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      passengerZalo: passengerZalo ?? this.passengerZalo,
      status: status ?? this.status,
      isAsap: isAsap ?? this.isAsap,
      province: province ?? this.province,
      district: district ?? this.district,
      commune: commune ?? this.commune,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class RideWithDrivers extends Equatable {
  const RideWithDrivers({
    required this.id,
    required this.creatorId,
    this.acceptorId,
    required this.vehicleType,
    required this.price,
    required this.commissionRate,
    required this.commissionAmount,
    required this.pickupTime,
    required this.pickupAddress,
    required this.passengerZalo,
    this.status = RideStatus.waiting,
    this.isAsap = false,
    this.province,
    this.district,
    this.commune,
    this.createdAt,
    this.updatedAt,
    required this.creator,
    this.acceptor,
  });

  final String id;
  final String creatorId;
  final String? acceptorId;
  final VehicleType vehicleType;
  final int price;
  final int commissionRate;
  final int commissionAmount;
  final DateTime pickupTime;
  final String pickupAddress;
  final String passengerZalo;
  final RideStatus status;
  final bool isAsap;
  final String? province;
  final String? district;
  final String? commune;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Driver creator;
  final Driver? acceptor;

  @override
  List<Object?> get props => [
        id,
        creatorId,
        acceptorId,
        vehicleType,
        price,
        commissionRate,
        commissionAmount,
        pickupTime,
        pickupAddress,
        passengerZalo,
        status,
        isAsap,
        province,
        district,
        commune,
        createdAt,
        updatedAt,
        creator,
        acceptor,
      ];
}

class CreateRide extends Equatable {
  const CreateRide({
    required this.creatorId,
    required this.vehicleType,
    required this.price,
    required this.commissionRate,
    required this.pickupTime,
    required this.pickupAddress,
    required this.passengerZalo,
    this.isAsap = false,
    this.province,
    this.district,
    this.commune,
  });

  final String creatorId;
  final VehicleType vehicleType;
  final int price;
  final int commissionRate;
  final DateTime pickupTime;
  final String pickupAddress;
  final String passengerZalo;
  final bool isAsap;
  final String? province;
  final String? district;
  final String? commune;

  @override
  List<Object?> get props => [
        creatorId,
        vehicleType,
        price,
        commissionRate,
        pickupTime,
        pickupAddress,
        passengerZalo,
        isAsap,
        province,
        district,
        commune,
      ];
}
