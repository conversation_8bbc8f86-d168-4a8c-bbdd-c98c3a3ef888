import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/app/app_cubit.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../widgets/custom_button.dart';

class DriverDashboard extends StatefulWidget {
  const DriverDashboard({super.key});

  @override
  State<DriverDashboard> createState() => _DriverDashboardState();
}

class _DriverDashboardState extends State<DriverDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Tài Xế'),
        actions: [
          IconButton(
            onPressed: () {
              _showRoleSwitchDialog(context);
            },
            icon: const Icon(Icons.switch_account),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.list),
              text: 'Cuốc Xe',
            ),
            Tab(
              icon: Icon(Icons.add),
              text: 'Tạo Cuốc',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'Lịch Sử',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAvailableRidesTab(),
          _buildCreateRideTab(),
          _buildRideHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildAvailableRidesTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_taxi,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Danh sách cuốc xe khả dụng',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Sẽ được implement trong bước tiếp theo',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildCreateRideTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.add_circle_outline,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Tạo cuốc xe mới',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Form tạo cuốc xe sẽ được implement',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildRideHistoryTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Lịch sử cuốc xe',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Danh sách lịch sử cuốc xe của tài xế',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  void _showRoleSwitchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chuyển đổi vai trò'),
        content: const Text('Bạn có muốn chuyển sang vai trò Quản trị viên?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          CustomButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AppCubit>().switchUserRole('admin');
            },
            size: ButtonSize.small,
            child: const Text('Chuyển đổi'),
          ),
        ],
      ),
    );
  }
}
