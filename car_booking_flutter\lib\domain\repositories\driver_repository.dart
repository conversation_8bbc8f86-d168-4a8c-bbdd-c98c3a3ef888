import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../entities/driver.dart';

abstract class DriverRepository {
  Future<Either<Failure, List<DriverWithStats>>> getAllDrivers();
  Future<Either<Failure, Driver>> getDriver(String id);
  Future<Either<Failure, Driver>> getDriverByPhone(String phone);
  Future<Either<Failure, Driver>> createDriver(CreateDriver driver);
  Future<Either<Failure, void>> updateDriverStatus(String id, String status);
  Future<Either<Failure, void>> updateDriverLocation(String id, String location);
}
