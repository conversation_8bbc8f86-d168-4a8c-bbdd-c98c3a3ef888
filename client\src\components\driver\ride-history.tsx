import { useQuery } from "@tanstack/react-query";
import { Clock, MapPin, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import StatusBadge from "@/components/ui/status-badge";
import type { RideWithDrivers } from "@shared/schema";

interface RideHistoryProps {
  driverId: string;
}

export default function RideHistory({ driverId }: RideHistoryProps) {
  const { data: rides, isLoading, isFetching } = useQuery({
    queryKey: ['/api/rides/history', driverId],
    refetchInterval: false,
    staleTime: 4000,
  });

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + ' VNĐ';
  };

  const formatDate = (date: string) => {
    const now = new Date();
    const rideDate = new Date(date);
    const diffTime = now.getTime() - rideDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Hôm nay';
    if (diffDays === 1) return 'Hôm qua';
    if (diffDays < 7) return `${diffDays} ngày trước`;
    return rideDate.toLocaleDateString('vi-VN');
  };

  const formatTime = (date: string) => {
    return new Date(date).toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'in_progress': return 'yellow';
      case 'accepted': return 'blue';
      case 'cancelled': return 'red';
      default: return 'gray';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return 'Đã hoàn thành';
      case 'in_progress': return 'Đang thực hiện';
      case 'accepted': return 'Đã nhận';
      case 'cancelled': return 'Đã hủy';
      case 'waiting': return 'Đang chờ nhận';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'in_progress': return AlertCircle;
      case 'accepted': return AlertCircle;
      case 'cancelled': return XCircle;
      default: return AlertCircle;
    }
  };

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-200 rounded-lg h-24"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold mb-4">
        Lịch Sử Cuốc Xe
        {isFetching && (
          <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-1"></div>
            Đang cập nhật
          </span>
        )}
      </h2>
      
      {!rides || (rides as RideWithDrivers[])?.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Clock className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>Chưa có lịch sử cuốc xe</p>
        </div>
      ) : (
        <div className="space-y-4">
          {(rides as RideWithDrivers[]).map((ride) => {
            const StatusIcon = getStatusIcon(ride.status);
            const isCreator = ride.creatorId === driverId;
            
            return (
              <div
                key={ride.id}
                className={`bg-white border rounded-lg p-4 shadow-sm ${
                  ride.status === 'in_progress' ? 'border-orange-200 border-2' : 'border-gray-200'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <StatusBadge 
                    status={getStatusLabel(ride.status)}
                    variant={getStatusColor(ride.status) as any}
                  />
                  <span className="text-sm text-gray-500">{formatDate(ride.createdAt?.toString() || '')}</span>
                </div>
                
                <div className="space-y-1 mb-3">
                  <p className="font-semibold text-lg">{formatCurrency(ride.price)}</p>
                  <p className="text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-1 inline" />
                    {formatTime(ride.pickupTime.toString())} - {ride.pickupAddress}
                  </p>
                  <p className="text-sm text-gray-500">
                    {isCreator ? 'Bạn tạo cuốc' : `Tài xế: ${ride.creator?.name || 'N/A'}`}
                    {ride.acceptor && ride.acceptorId !== driverId && ` • Nhận bởi: ${ride.acceptor?.name || 'N/A'}`}
                  </p>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <StatusIcon className={`w-4 h-4 mr-1 ${
                      ride.status === 'completed' ? 'text-green-600' :
                      ride.status === 'cancelled' ? 'text-red-600' :
                      'text-orange-600'
                    }`} />
                    <span className="text-gray-600">
                      {ride.status === 'completed' && isCreator && 'Hoa hồng: '}
                      {ride.status === 'completed' && !isCreator && 'Nhận được: '}
                      {ride.status === 'cancelled' && 'Lý do: Hủy cuốc'}
                      {ride.status === 'in_progress' && 'Đang đón khách...'}
                    </span>
                  </div>
                  
                  {ride.status === 'completed' && (
                    <span className={`font-medium ${
                      isCreator ? 'text-green-600' : 'text-blue-600'
                    }`}>
                      {isCreator 
                        ? `+${formatCurrency(ride.commissionAmount)}`
                        : `+${formatCurrency(ride.price - ride.commissionAmount)}`
                      }
                    </span>
                  )}
                  
                  {ride.status === 'cancelled' && (
                    <span className="font-medium text-red-600">
                      -25.000 VNĐ
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
