import 'package:equatable/equatable.dart';

class SystemConfig extends Equatable {
  const SystemConfig({
    required this.id,
    this.defaultCommissionRate = 10,
    this.driverSharePercentage = 70,
    this.cancelPenalty = 25000,
    this.noPickupPenalty = 50000,
    this.missedPickupPenalty = 75000,
    this.warningTimeMinutes = 15,
    this.updatedAt,
  });

  final String id;
  final int defaultCommissionRate;
  final int driverSharePercentage;
  final int cancelPenalty;
  final int noPickupPenalty;
  final int missedPickupPenalty;
  final int warningTimeMinutes;
  final DateTime? updatedAt;

  @override
  List<Object?> get props => [
        id,
        defaultCommissionRate,
        driverSharePercentage,
        cancelPenalty,
        noPickupPenalty,
        missedPickupPenalty,
        warningTimeMinutes,
        updatedAt,
      ];

  SystemConfig copyWith({
    String? id,
    int? defaultCommissionRate,
    int? driverSharePercentage,
    int? cancelPenalty,
    int? noPickupPenalty,
    int? missedPickupPenalty,
    int? warningTimeMinutes,
    DateTime? updatedAt,
  }) {
    return SystemConfig(
      id: id ?? this.id,
      defaultCommissionRate: defaultCommissionRate ?? this.defaultCommissionRate,
      driverSharePercentage: driverSharePercentage ?? this.driverSharePercentage,
      cancelPenalty: cancelPenalty ?? this.cancelPenalty,
      noPickupPenalty: noPickupPenalty ?? this.noPickupPenalty,
      missedPickupPenalty: missedPickupPenalty ?? this.missedPickupPenalty,
      warningTimeMinutes: warningTimeMinutes ?? this.warningTimeMinutes,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class UpdateSystemConfig extends Equatable {
  const UpdateSystemConfig({
    this.defaultCommissionRate,
    this.driverSharePercentage,
    this.cancelPenalty,
    this.noPickupPenalty,
    this.missedPickupPenalty,
    this.warningTimeMinutes,
  });

  final int? defaultCommissionRate;
  final int? driverSharePercentage;
  final int? cancelPenalty;
  final int? noPickupPenalty;
  final int? missedPickupPenalty;
  final int? warningTimeMinutes;

  @override
  List<Object?> get props => [
        defaultCommissionRate,
        driverSharePercentage,
        cancelPenalty,
        noPickupPenalty,
        missedPickupPenalty,
        warningTimeMinutes,
      ];
}
