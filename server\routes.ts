import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { insertDriverSchema, insertRideSchema, insertSystemConfigSchema } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // WebSocket server for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });
  
  const connectedClients = new Set<WebSocket>();

  wss.on('connection', (ws) => {
    connectedClients.add(ws);
    
    ws.on('close', () => {
      connectedClients.delete(ws);
    });
  });

  // Broadcast function for real-time updates
  const broadcastUpdate = (type: string, data: any) => {
    const message = JSON.stringify({ type, data });
    connectedClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  };

  // Driver routes
  app.post("/api/drivers", async (req, res) => {
    try {
      const driverData = insertDriverSchema.parse(req.body);
      const driver = await storage.createDriver(driverData);
      broadcastUpdate('driver_created', driver);
      res.json(driver);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : 'Invalid driver data' });
    }
  });

  app.get("/api/drivers", async (req, res) => {
    try {
      const drivers = await storage.getAllDrivers();
      res.json(drivers);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch drivers' });
    }
  });

  app.get("/api/drivers/:phone", async (req, res) => {
    try {
      const driver = await storage.getDriverByPhone(req.params.phone);
      if (!driver) {
        return res.status(404).json({ message: 'Driver not found' });
      }
      res.json(driver);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch driver' });
    }
  });

  app.patch("/api/drivers/:id/status", async (req, res) => {
    try {
      const { status } = req.body;
      if (!['active', 'inactive', 'busy'].includes(status)) {
        return res.status(400).json({ message: 'Invalid status' });
      }
      await storage.updateDriverStatus(req.params.id, status);
      broadcastUpdate('driver_status_updated', { id: req.params.id, status });
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: 'Failed to update driver status' });
    }
  });

  app.patch("/api/drivers/:id/location", async (req, res) => {
    try {
      const { location } = req.body;
      await storage.updateDriverLocation(req.params.id, location);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: 'Failed to update driver location' });
    }
  });

  // Ride routes
  app.post("/api/rides", async (req, res) => {
    try {
      const rideData = insertRideSchema.parse(req.body);
      const ride = await storage.createRide(rideData);
      const rideWithDrivers = await storage.getRide(ride.id);
      broadcastUpdate('ride_created', rideWithDrivers);
      res.json(rideWithDrivers);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : 'Invalid ride data' });
    }
  });

  app.get("/api/rides", async (req, res) => {
    try {
      const rides = await storage.getAllRides();
      res.json(rides);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch rides' });
    }
  });

  app.get("/api/rides/available", async (req, res) => {
    try {
      const { province, district, commune } = req.query;
      const location = province || district || commune ? {
        province: province as string,
        district: district as string,
        commune: commune as string,
      } : undefined;
      
      const rides = await storage.getAvailableRides(location);
      res.json(rides);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch available rides' });
    }
  });

  app.get("/api/rides/history/:driverId", async (req, res) => {
    try {
      const rides = await storage.getRideHistory(req.params.driverId);
      res.json(rides);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch ride history' });
    }
  });

  app.post("/api/rides/:id/accept", async (req, res) => {
    try {
      const { driverId } = req.body;
      if (!driverId) {
        return res.status(400).json({ message: 'Driver ID is required' });
      }
      
      await storage.acceptRide(req.params.id, driverId);
      const updatedRide = await storage.getRide(req.params.id);
      broadcastUpdate('ride_accepted', updatedRide);
      res.json(updatedRide);
    } catch (error) {
      res.status(500).json({ message: 'Failed to accept ride' });
    }
  });

  app.patch("/api/rides/:id/status", async (req, res) => {
    try {
      const { status } = req.body;
      if (!['waiting', 'accepted', 'in_progress', 'completed', 'cancelled'].includes(status)) {
        return res.status(400).json({ message: 'Invalid status' });
      }
      
      await storage.updateRideStatus(req.params.id, status);
      const updatedRide = await storage.getRide(req.params.id);
      broadcastUpdate('ride_status_updated', updatedRide);
      res.json(updatedRide);
    } catch (error) {
      res.status(500).json({ message: 'Failed to update ride status' });
    }
  });

  // System configuration routes
  app.get("/api/config", async (req, res) => {
    try {
      const config = await storage.getSystemConfig();
      res.json(config);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch system configuration' });
    }
  });

  app.patch("/api/config", async (req, res) => {
    try {
      const configData = insertSystemConfigSchema.partial().parse(req.body);
      const config = await storage.updateSystemConfig(configData);
      res.json(config);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : 'Invalid configuration data' });
    }
  });

  // Dashboard statistics
  app.get("/api/dashboard/stats", async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch dashboard statistics' });
    }
  });

  return httpServer;
}
