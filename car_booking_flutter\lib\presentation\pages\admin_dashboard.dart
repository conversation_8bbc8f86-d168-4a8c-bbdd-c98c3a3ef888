import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/app/app_cubit.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../widgets/custom_button.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Quản Trị'),
        actions: [
          IconButton(
            onPressed: () {
              _showRoleSwitchDialog(context);
            },
            icon: const Icon(Icons.switch_account),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(
              icon: Icon(Icons.assignment),
              text: 'Quản Lý Cuốc',
            ),
            Tab(
              icon: Icon(Icons.people),
              text: 'Quản Lý Tài Xế',
            ),
            Tab(
              icon: Icon(Icons.settings),
              text: 'Cấu Hình',
            ),
            Tab(
              icon: Icon(Icons.analytics),
              text: 'Báo Cáo',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRideManagementTab(),
          _buildDriverManagementTab(),
          _buildSystemConfigTab(),
          _buildReportsTab(),
        ],
      ),
    );
  }

  Widget _buildRideManagementTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Quản lý cuốc xe',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Danh sách và quản lý tất cả cuốc xe',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildDriverManagementTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Quản lý tài xế',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Danh sách và thống kê tài xế',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemConfigTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Cấu hình hệ thống',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Cài đặt hoa hồng, phạt và các thông số',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'Báo cáo thống kê',
            style: AppTextStyles.h5,
          ),
          SizedBox(height: 8),
          Text(
            'Biểu đồ và báo cáo doanh thu',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  void _showRoleSwitchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chuyển đổi vai trò'),
        content: const Text('Bạn có muốn chuyển sang vai trò Tài xế?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          CustomButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AppCubit>().switchUserRole('driver');
            },
            size: ButtonSize.small,
            child: const Text('Chuyển đổi'),
          ),
        ],
      ),
    );
  }
}
