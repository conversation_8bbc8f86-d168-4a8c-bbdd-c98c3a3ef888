import '../../core/constants/api_constants.dart';
import '../models/system_config_model.dart';
import '../models/dashboard_stats_model.dart';
import '../models/penalty_model.dart';
import 'api_client.dart';

abstract class SystemRemoteDataSource {
  Future<SystemConfigModel> getSystemConfig();
  Future<SystemConfigModel> updateSystemConfig(UpdateSystemConfigModel config);
  Future<DashboardStatsModel> getDashboardStats();
  Future<List<PenaltyModel>> getDriverPenalties(String driverId);
  Future<PenaltyModel> createPenalty(CreatePenaltyModel penalty);
}

class SystemRemoteDataSourceImpl implements SystemRemoteDataSource {
  final ApiClient _apiClient;

  SystemRemoteDataSourceImpl(this._apiClient);

  @override
  Future<SystemConfigModel> getSystemConfig() async {
    final response = await _apiClient.get(ApiConstants.config);
    return SystemConfigModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<SystemConfigModel> updateSystemConfig(UpdateSystemConfigModel config) async {
    final response = await _apiClient.patch(
      ApiConstants.config,
      data: config.toJson(),
    );
    return SystemConfigModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<DashboardStatsModel> getDashboardStats() async {
    final response = await _apiClient.get(ApiConstants.dashboardStats);
    return DashboardStatsModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<List<PenaltyModel>> getDriverPenalties(String driverId) async {
    final response = await _apiClient.get(ApiConstants.driverPenalties(driverId));
    final List<dynamic> data = response.data as List<dynamic>;
    
    return data
        .map((json) => PenaltyModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<PenaltyModel> createPenalty(CreatePenaltyModel penalty) async {
    final response = await _apiClient.post(
      '/api/penalties',
      data: penalty.toJson(),
    );
    return PenaltyModel.fromJson(response.data as Map<String, dynamic>);
  }
}
