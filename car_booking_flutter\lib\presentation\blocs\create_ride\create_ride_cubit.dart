import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/ride.dart';
import '../../../domain/usecases/create_ride.dart';

// States
abstract class CreateRideState extends Equatable {
  const CreateRideState();

  @override
  List<Object?> get props => [];
}

class CreateRideInitial extends CreateRideState {}

class CreateRideLoading extends CreateRideState {}

class CreateRideSuccess extends CreateRideState {
  const CreateRideSuccess(this.ride);

  final RideWithDrivers ride;

  @override
  List<Object?> get props => [ride];
}

class CreateRideError extends CreateRideState {
  const CreateRideError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

// Cubit
class CreateRideCubit extends Cubit<CreateRideState> {
  final CreateRideUseCase _createRide;

  CreateRideCubit({
    required CreateRideUseCase createRide,
  })  : _createRide = createRide,
        super(CreateRideInitial());

  Future<void> createRide(CreateRide ride) async {
    emit(CreateRideLoading());
    
    final result = await _createRide(ride);
    
    result.fold(
      (failure) => emit(CreateRideError(failure.message)),
      (createdRide) => emit(CreateRideSuccess(createdRide)),
    );
  }

  void reset() {
    emit(CreateRideInitial());
  }
}
