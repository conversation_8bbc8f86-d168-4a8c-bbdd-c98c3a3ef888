import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/constants/enums.dart';
import '../../domain/entities/ride.dart';
import 'driver_model.dart';

part 'ride_model.freezed.dart';
part 'ride_model.g.dart';

@freezed
class RideModel with _$RideModel {
  const factory RideModel({
    required String id,
    required String creatorId,
    String? acceptorId,
    required VehicleType vehicleType,
    required int price,
    required int commissionRate,
    required int commissionAmount,
    required DateTime pickupTime,
    required String pickupAddress,
    required String passengerZalo,
    @Default(RideStatus.waiting) RideStatus status,
    @Default(false) bool isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _RideModel;

  factory RideModel.fromJson(Map<String, dynamic> json) => _$RideModelFromJson(json);
}

@freezed
class RideWithDriversModel with _$RideWithDriversModel {
  const factory RideWithDriversModel({
    required String id,
    required String creatorId,
    String? acceptorId,
    required VehicleType vehicleType,
    required int price,
    required int commissionRate,
    required int commissionAmount,
    required DateTime pickupTime,
    required String pickupAddress,
    required String passengerZalo,
    @Default(RideStatus.waiting) RideStatus status,
    @Default(false) bool isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
    required DriverModel creator,
    DriverModel? acceptor,
  }) = _RideWithDriversModel;

  factory RideWithDriversModel.fromJson(Map<String, dynamic> json) => _$RideWithDriversModelFromJson(json);
}

@freezed
class CreateRideModel with _$CreateRideModel {
  const factory CreateRideModel({
    required String creatorId,
    required VehicleType vehicleType,
    required int price,
    required int commissionRate,
    required DateTime pickupTime,
    required String pickupAddress,
    required String passengerZalo,
    @Default(false) bool isAsap,
    String? province,
    String? district,
    String? commune,
  }) = _CreateRideModel;

  factory CreateRideModel.fromJson(Map<String, dynamic> json) => _$CreateRideModelFromJson(json);
}

// Extensions to convert between models and entities
extension RideModelX on RideModel {
  Ride toEntity() {
    return Ride(
      id: id,
      creatorId: creatorId,
      acceptorId: acceptorId,
      vehicleType: vehicleType,
      price: price,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      pickupTime: pickupTime,
      pickupAddress: pickupAddress,
      passengerZalo: passengerZalo,
      status: status,
      isAsap: isAsap,
      province: province,
      district: district,
      commune: commune,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

extension RideWithDriversModelX on RideWithDriversModel {
  RideWithDrivers toEntity() {
    return RideWithDrivers(
      id: id,
      creatorId: creatorId,
      acceptorId: acceptorId,
      vehicleType: vehicleType,
      price: price,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      pickupTime: pickupTime,
      pickupAddress: pickupAddress,
      passengerZalo: passengerZalo,
      status: status,
      isAsap: isAsap,
      province: province,
      district: district,
      commune: commune,
      createdAt: createdAt,
      updatedAt: updatedAt,
      creator: creator.toEntity(),
      acceptor: acceptor?.toEntity(),
    );
  }
}

extension CreateRideModelX on CreateRideModel {
  CreateRide toEntity() {
    return CreateRide(
      creatorId: creatorId,
      vehicleType: vehicleType,
      price: price,
      commissionRate: commissionRate,
      pickupTime: pickupTime,
      pickupAddress: pickupAddress,
      passengerZalo: passengerZalo,
      isAsap: isAsap,
      province: province,
      district: district,
      commune: commune,
    );
  }
}
