import { useQuery } from "@tanstack/react-query";
import { User, Plus, Phone, MapPin, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import StatusBadge from "@/components/ui/status-badge";
import type { DriverWithStats } from "@shared/schema";

export default function DriverManagement() {
  const { data: drivers, isLoading, isFetching } = useQuery({
    queryKey: ['/api/drivers'],
    refetchInterval: false,
    staleTime: 4000,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'busy': return 'yellow';
      case 'inactive': return 'red';
      default: return 'gray';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'busy': return 'Bận';
      case 'inactive': return 'Offline';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Danh sách tài xế
            {isFetching && (
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-1"></div>
                Đang cập nhật
              </span>
            )}
          </h3>
          <Button className="bg-primary hover:bg-green-700 text-white">
            <Plus className="w-4 h-4 mr-2" />
            Thêm tài xế
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(drivers as DriverWithStats[])?.map((driver) => (
            <div key={driver.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-gray-600" />
                </div>
                <div className="ml-3 flex-1">
                  <h4 className="text-sm font-medium text-gray-900">{driver.name}</h4>
                  <p className="text-sm text-gray-500">
                    <Phone className="w-3 h-3 mr-1 inline" />
                    {driver.phone}
                  </p>
                </div>
                <div className="ml-auto">
                  <StatusBadge
                    status={getStatusLabel(driver.status || 'inactive')}
                    variant={getStatusColor(driver.status || 'inactive') as any}
                  />
                </div>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Cuốc hoàn thành:</span>
                  <span className="font-medium">{driver.completedRides}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Đánh giá:</span>
                  <div className="flex items-center">
                    <Star className="w-3 h-3 text-yellow-400 mr-1" />
                    <span className="font-medium">{driver.rating}/5.0</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Vị trí hiện tại:</span>
                  <span className="font-medium">
                    <MapPin className="w-3 h-3 mr-1 inline" />
                    {driver.currentLocation || 'Chưa cập nhật'}
                  </span>
                </div>
                {driver.zalo && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Zalo:</span>
                    <span className="font-medium">{driver.zalo}</span>
                  </div>
                )}
              </div>

              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    Chi tiết
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Liên hệ
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {(!drivers || (drivers as DriverWithStats[])?.length === 0) && (
          <div className="text-center py-8 text-gray-500">
            <User className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Chưa có tài xế nào</p>
          </div>
        )}
      </div>
    </div>
  );
}
