import { RotateCcw, Wifi, WifiOff } from "lucide-react";
import { cn } from "@/lib/utils";

interface RefreshIndicatorProps {
  countdown: number;
  isOnline: boolean;
  className?: string;
}

export default function RefreshIndicator({ countdown, isOnline, className }: RefreshIndicatorProps) {
  return (
    <div className={cn(
      "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors",
      isOnline 
        ? "bg-green-50 border border-green-200 text-green-800" 
        : "bg-red-50 border border-red-200 text-red-800",
      className
    )}>
      {isOnline ? (
        <Wifi className="w-4 h-4" />
      ) : (
        <WifiOff className="w-4 h-4" />
      )}
      
      <RotateCcw className={cn(
        "w-4 h-4 transition-transform",
        countdown <= 1 && isOnline ? "animate-spin" : ""
      )} />
      
      <span className="font-medium">
        {isOnline 
          ? `Cập nhật sau ${countdown}s` 
          : "Mất kết nối"
        }
      </span>
      
      {isOnline && (
        <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-green-500 transition-all duration-1000 ease-linear"
            style={{ 
              width: `${((5 - countdown) / 5) * 100}%` 
            }}
          />
        </div>
      )}
    </div>
  );
}