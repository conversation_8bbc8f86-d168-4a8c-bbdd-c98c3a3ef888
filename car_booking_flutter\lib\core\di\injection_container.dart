import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get_it/get_it.dart';

// Data sources
import '../../data/datasources/api_client.dart';
import '../../data/datasources/websocket_service.dart';
import '../../data/datasources/local_storage_service.dart';
import '../../data/datasources/driver_remote_data_source.dart';
import '../../data/datasources/ride_remote_data_source.dart';
import '../../data/datasources/system_remote_data_source.dart';

// Repositories
import '../../domain/repositories/driver_repository.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../domain/repositories/system_repository.dart';
import '../../data/repositories/driver_repository_impl.dart';
import '../../data/repositories/ride_repository_impl.dart';
import '../../data/repositories/system_repository_impl.dart';

// Use cases
import '../../domain/usecases/get_available_rides.dart';
import '../../domain/usecases/create_ride.dart';
import '../../domain/usecases/accept_ride.dart';
import '../../domain/usecases/get_ride_history.dart';
import '../../domain/usecases/get_all_drivers.dart';
import '../../domain/usecases/get_dashboard_stats.dart';

// Blocs
import '../../presentation/blocs/app/app_cubit.dart';
import '../../presentation/blocs/available_rides/available_rides_bloc.dart';
import '../../presentation/blocs/create_ride/create_ride_cubit.dart';
import '../../presentation/blocs/ride_history/ride_history_cubit.dart';
import '../../presentation/blocs/driver_management/driver_management_cubit.dart';
import '../../presentation/blocs/dashboard_stats/dashboard_stats_cubit.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Initialize local storage first
  await LocalStorageService.init();

  //! External dependencies
  sl.registerLazySingleton(() => Connectivity());

  //! Core
  sl.registerLazySingleton(() => ApiClient());
  sl.registerLazySingleton(() => WebSocketService());
  sl.registerLazySingleton(() => LocalStorageService.instance);

  //! Data sources
  sl.registerLazySingleton<DriverRemoteDataSource>(
    () => DriverRemoteDataSourceImpl(sl()),
  );
  sl.registerLazySingleton<RideRemoteDataSource>(
    () => RideRemoteDataSourceImpl(sl()),
  );
  sl.registerLazySingleton<SystemRemoteDataSource>(
    () => SystemRemoteDataSourceImpl(sl()),
  );

  //! Repositories
  sl.registerLazySingleton<DriverRepository>(
    () => DriverRepositoryImpl(sl(), sl()),
  );
  sl.registerLazySingleton<RideRepository>(
    () => RideRepositoryImpl(sl(), sl()),
  );
  sl.registerLazySingleton<SystemRepository>(
    () => SystemRepositoryImpl(sl(), sl()),
  );

  //! Use cases
  sl.registerLazySingleton(() => GetAvailableRides(sl()));
  sl.registerLazySingleton(() => CreateRideUseCase(sl()));
  sl.registerLazySingleton(() => AcceptRideUseCase(sl()));
  sl.registerLazySingleton(() => GetRideHistoryUseCase(sl()));
  sl.registerLazySingleton(() => GetAllDriversUseCase(sl()));
  sl.registerLazySingleton(() => GetDashboardStatsUseCase(sl()));

  //! Blocs
  sl.registerFactory(() => AppCubit(
        webSocketService: sl(),
        localStorage: sl(),
        connectivity: sl(),
      ));

  sl.registerFactory(() => AvailableRidesBloc(
        getAvailableRides: sl(),
        acceptRide: sl(),
        webSocketService: sl(),
      ));

  sl.registerFactory(() => CreateRideCubit(
        createRide: sl(),
      ));

  sl.registerFactory(() => RideHistoryCubit(
        getRideHistory: sl(),
      ));

  sl.registerFactory(() => DriverManagementCubit(
        getAllDrivers: sl(),
      ));

  sl.registerFactory(() => DashboardStatsCubit(
        getDashboardStats: sl(),
        webSocketService: sl(),
      ));
}
