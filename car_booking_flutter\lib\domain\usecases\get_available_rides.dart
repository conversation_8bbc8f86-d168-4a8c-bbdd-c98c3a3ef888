import '../../core/errors/failures.dart';
import '../../core/utils/either.dart';
import '../entities/ride.dart';
import '../repositories/ride_repository.dart';

class GetAvailableRides {
  final RideRepository _repository;

  GetAvailableRides(this._repository);

  Future<Either<Failure, List<RideWithDrivers>>> call({LocationFilter? location}) {
    return _repository.getAvailableRides(location: location);
  }
}
