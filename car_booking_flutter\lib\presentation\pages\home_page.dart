import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/app/app_cubit.dart';
import 'driver_dashboard.dart';
import 'admin_dashboard.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../widgets/custom_button.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppCubit, AppState>(
      builder: (context, state) {
        if (state is! AppReady) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // If no user role is set, show role selection
        if (state.userRole.isEmpty) {
          return _buildRoleSelection(context);
        }

        // Navigate to appropriate dashboard based on role
        switch (state.userRole) {
          case 'driver':
            return const DriverDashboard();
          case 'admin':
            return const AdminDashboard();
          default:
            return _buildRoleSelection(context);
        }
      },
    );
  }

  Widget _buildRoleSelection(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo or app icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(
                  Icons.local_taxi,
                  size: 64,
                  color: AppColors.white,
                ),
              ),
              const SizedBox(height: 32),
              
              // App title
              Text(
                'Hệ Thống Đặt Xe',
                style: AppTextStyles.h2.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Chọn vai trò của bạn để tiếp tục',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // Role selection buttons
              CustomButton(
                onPressed: () {
                  context.read<AppCubit>().switchUserRole('driver');
                },
                variant: ButtonVariant.primary,
                size: ButtonSize.large,
                isFullWidth: true,
                icon: const Icon(Icons.drive_eta),
                child: const Text('Tài Xế'),
              ),
              const SizedBox(height: 16),
              CustomButton(
                onPressed: () {
                  context.read<AppCubit>().switchUserRole('admin');
                },
                variant: ButtonVariant.outline,
                size: ButtonSize.large,
                isFullWidth: true,
                icon: const Icon(Icons.admin_panel_settings),
                child: const Text('Quản Trị Viên'),
              ),
              
              const SizedBox(height: 48),
              
              // Connection status
              BlocBuilder<AppCubit, AppState>(
                builder: (context, state) {
                  if (state is AppReady) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          state.isOnline ? Icons.wifi : Icons.wifi_off,
                          size: 16,
                          color: state.isOnline ? AppColors.success : AppColors.error,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          state.isOnline ? 'Đã kết nối' : 'Mất kết nối',
                          style: AppTextStyles.labelMedium.copyWith(
                            color: state.isOnline ? AppColors.success : AppColors.error,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          state.isWebSocketConnected ? Icons.sync : Icons.sync_disabled,
                          size: 16,
                          color: state.isWebSocketConnected ? AppColors.success : AppColors.warning,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          state.isWebSocketConnected ? 'Real-time' : 'Offline',
                          style: AppTextStyles.labelMedium.copyWith(
                            color: state.isWebSocketConnected ? AppColors.success : AppColors.warning,
                          ),
                        ),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
