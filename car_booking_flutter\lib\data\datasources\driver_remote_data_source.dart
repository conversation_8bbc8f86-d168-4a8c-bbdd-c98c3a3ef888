import '../../core/constants/api_constants.dart';
import '../models/driver_model.dart';
import 'api_client.dart';

abstract class DriverRemoteDataSource {
  Future<List<DriverWithStatsModel>> getAllDrivers();
  Future<DriverModel> getDriver(String id);
  Future<DriverModel> getDriverByPhone(String phone);
  Future<DriverModel> createDriver(CreateDriverModel driver);
  Future<void> updateDriverStatus(String id, String status);
  Future<void> updateDriverLocation(String id, String location);
}

class DriverRemoteDataSourceImpl implements DriverRemoteDataSource {
  final ApiClient _apiClient;

  DriverRemoteDataSourceImpl(this._apiClient);

  @override
  Future<List<DriverWithStatsModel>> getAllDrivers() async {
    final response = await _apiClient.get(ApiConstants.drivers);
    final List<dynamic> data = response.data as List<dynamic>;
    
    return data
        .map((json) => DriverWithStatsModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<DriverModel> getDriver(String id) async {
    final response = await _apiClient.get('${ApiConstants.drivers}/$id');
    return DriverModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<DriverModel> getDriverByPhone(String phone) async {
    final response = await _apiClient.get(
      ApiConstants.drivers,
      queryParameters: {'phone': phone},
    );
    return DriverModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<DriverModel> createDriver(CreateDriverModel driver) async {
    final response = await _apiClient.post(
      ApiConstants.drivers,
      data: driver.toJson(),
    );
    return DriverModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<void> updateDriverStatus(String id, String status) async {
    await _apiClient.patch(
      '${ApiConstants.drivers}/$id/status',
      data: {'status': status},
    );
  }

  @override
  Future<void> updateDriverLocation(String id, String location) async {
    await _apiClient.patch(
      '${ApiConstants.drivers}/$id/location',
      data: {'location': location},
    );
  }
}
