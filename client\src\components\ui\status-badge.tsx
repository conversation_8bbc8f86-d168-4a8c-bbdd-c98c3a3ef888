import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: string;
  variant?: 'green' | 'yellow' | 'blue' | 'red' | 'gray';
  className?: string;
}

export default function StatusBadge({ status, variant = 'gray', className }: StatusBadgeProps) {
  const variantClasses = {
    green: 'bg-green-100 text-green-800',
    yellow: 'bg-yellow-100 text-yellow-800',
    blue: 'bg-blue-100 text-blue-800',
    red: 'bg-red-100 text-red-800',
    gray: 'bg-gray-100 text-gray-800',
  };

  return (
    <span
      className={cn(
        'inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium',
        variantClasses[variant],
        className
      )}
    >
      {status}
    </span>
  );
}
