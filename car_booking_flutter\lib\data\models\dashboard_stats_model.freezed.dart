// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_stats_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

DashboardStatsModel _$DashboardStatsModelFromJson(Map<String, dynamic> json) {
  return _DashboardStatsModel.fromJson(json);
}

/// @nodoc
mixin _$DashboardStatsModel {
  int get todayRides => throw _privateConstructorUsedError;
  int get activeDrivers => throw _privateConstructorUsedError;
  int get todayRevenue => throw _privateConstructorUsedError;
  int get todayCommission => throw _privateConstructorUsedError;

  /// Serializes this DashboardStatsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DashboardStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DashboardStatsModelCopyWith<DashboardStatsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DashboardStatsModelCopyWith<$Res> {
  factory $DashboardStatsModelCopyWith(
    DashboardStatsModel value,
    $Res Function(DashboardStatsModel) then,
  ) = _$DashboardStatsModelCopyWithImpl<$Res, DashboardStatsModel>;
  @useResult
  $Res call({
    int todayRides,
    int activeDrivers,
    int todayRevenue,
    int todayCommission,
  });
}

/// @nodoc
class _$DashboardStatsModelCopyWithImpl<$Res, $Val extends DashboardStatsModel>
    implements $DashboardStatsModelCopyWith<$Res> {
  _$DashboardStatsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DashboardStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayRides = null,
    Object? activeDrivers = null,
    Object? todayRevenue = null,
    Object? todayCommission = null,
  }) {
    return _then(
      _value.copyWith(
            todayRides: null == todayRides
                ? _value.todayRides
                : todayRides // ignore: cast_nullable_to_non_nullable
                      as int,
            activeDrivers: null == activeDrivers
                ? _value.activeDrivers
                : activeDrivers // ignore: cast_nullable_to_non_nullable
                      as int,
            todayRevenue: null == todayRevenue
                ? _value.todayRevenue
                : todayRevenue // ignore: cast_nullable_to_non_nullable
                      as int,
            todayCommission: null == todayCommission
                ? _value.todayCommission
                : todayCommission // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DashboardStatsModelImplCopyWith<$Res>
    implements $DashboardStatsModelCopyWith<$Res> {
  factory _$$DashboardStatsModelImplCopyWith(
    _$DashboardStatsModelImpl value,
    $Res Function(_$DashboardStatsModelImpl) then,
  ) = __$$DashboardStatsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int todayRides,
    int activeDrivers,
    int todayRevenue,
    int todayCommission,
  });
}

/// @nodoc
class __$$DashboardStatsModelImplCopyWithImpl<$Res>
    extends _$DashboardStatsModelCopyWithImpl<$Res, _$DashboardStatsModelImpl>
    implements _$$DashboardStatsModelImplCopyWith<$Res> {
  __$$DashboardStatsModelImplCopyWithImpl(
    _$DashboardStatsModelImpl _value,
    $Res Function(_$DashboardStatsModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DashboardStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayRides = null,
    Object? activeDrivers = null,
    Object? todayRevenue = null,
    Object? todayCommission = null,
  }) {
    return _then(
      _$DashboardStatsModelImpl(
        todayRides: null == todayRides
            ? _value.todayRides
            : todayRides // ignore: cast_nullable_to_non_nullable
                  as int,
        activeDrivers: null == activeDrivers
            ? _value.activeDrivers
            : activeDrivers // ignore: cast_nullable_to_non_nullable
                  as int,
        todayRevenue: null == todayRevenue
            ? _value.todayRevenue
            : todayRevenue // ignore: cast_nullable_to_non_nullable
                  as int,
        todayCommission: null == todayCommission
            ? _value.todayCommission
            : todayCommission // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DashboardStatsModelImpl implements _DashboardStatsModel {
  const _$DashboardStatsModelImpl({
    this.todayRides = 0,
    this.activeDrivers = 0,
    this.todayRevenue = 0,
    this.todayCommission = 0,
  });

  factory _$DashboardStatsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DashboardStatsModelImplFromJson(json);

  @override
  @JsonKey()
  final int todayRides;
  @override
  @JsonKey()
  final int activeDrivers;
  @override
  @JsonKey()
  final int todayRevenue;
  @override
  @JsonKey()
  final int todayCommission;

  @override
  String toString() {
    return 'DashboardStatsModel(todayRides: $todayRides, activeDrivers: $activeDrivers, todayRevenue: $todayRevenue, todayCommission: $todayCommission)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DashboardStatsModelImpl &&
            (identical(other.todayRides, todayRides) ||
                other.todayRides == todayRides) &&
            (identical(other.activeDrivers, activeDrivers) ||
                other.activeDrivers == activeDrivers) &&
            (identical(other.todayRevenue, todayRevenue) ||
                other.todayRevenue == todayRevenue) &&
            (identical(other.todayCommission, todayCommission) ||
                other.todayCommission == todayCommission));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    todayRides,
    activeDrivers,
    todayRevenue,
    todayCommission,
  );

  /// Create a copy of DashboardStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DashboardStatsModelImplCopyWith<_$DashboardStatsModelImpl> get copyWith =>
      __$$DashboardStatsModelImplCopyWithImpl<_$DashboardStatsModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$DashboardStatsModelImplToJson(this);
  }
}

abstract class _DashboardStatsModel implements DashboardStatsModel {
  const factory _DashboardStatsModel({
    final int todayRides,
    final int activeDrivers,
    final int todayRevenue,
    final int todayCommission,
  }) = _$DashboardStatsModelImpl;

  factory _DashboardStatsModel.fromJson(Map<String, dynamic> json) =
      _$DashboardStatsModelImpl.fromJson;

  @override
  int get todayRides;
  @override
  int get activeDrivers;
  @override
  int get todayRevenue;
  @override
  int get todayCommission;

  /// Create a copy of DashboardStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DashboardStatsModelImplCopyWith<_$DashboardStatsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
