// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ride_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RideModelImpl _$$RideModelImplFromJson(Map<String, dynamic> json) =>
    _$RideModelImpl(
      id: json['id'] as String,
      creatorId: json['creatorId'] as String,
      acceptorId: json['acceptorId'] as String?,
      vehicleType: $enumDecode(_$VehicleTypeEnumMap, json['vehicleType']),
      price: (json['price'] as num).toInt(),
      commissionRate: (json['commissionRate'] as num).toInt(),
      commissionAmount: (json['commissionAmount'] as num).toInt(),
      pickupTime: DateTime.parse(json['pickupTime'] as String),
      pickupAddress: json['pickupAddress'] as String,
      passengerZalo: json['passengerZalo'] as String,
      status:
          $enumDecodeNullable(_$RideStatusEnumMap, json['status']) ??
          RideStatus.waiting,
      isAsap: json['isAsap'] as bool? ?? false,
      province: json['province'] as String?,
      district: json['district'] as String?,
      commune: json['commune'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$RideModelImplToJson(_$RideModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'creatorId': instance.creatorId,
      'acceptorId': instance.acceptorId,
      'vehicleType': _$VehicleTypeEnumMap[instance.vehicleType]!,
      'price': instance.price,
      'commissionRate': instance.commissionRate,
      'commissionAmount': instance.commissionAmount,
      'pickupTime': instance.pickupTime.toIso8601String(),
      'pickupAddress': instance.pickupAddress,
      'passengerZalo': instance.passengerZalo,
      'status': _$RideStatusEnumMap[instance.status]!,
      'isAsap': instance.isAsap,
      'province': instance.province,
      'district': instance.district,
      'commune': instance.commune,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$VehicleTypeEnumMap = {
  VehicleType.fourSeats: 'fourSeats',
  VehicleType.sevenSeats: 'sevenSeats',
  VehicleType.nineSeats: 'nineSeats',
  VehicleType.sixteenSeats: 'sixteenSeats',
};

const _$RideStatusEnumMap = {
  RideStatus.waiting: 'waiting',
  RideStatus.accepted: 'accepted',
  RideStatus.inProgress: 'inProgress',
  RideStatus.completed: 'completed',
  RideStatus.cancelled: 'cancelled',
};

_$RideWithDriversModelImpl _$$RideWithDriversModelImplFromJson(
  Map<String, dynamic> json,
) => _$RideWithDriversModelImpl(
  id: json['id'] as String,
  creatorId: json['creatorId'] as String,
  acceptorId: json['acceptorId'] as String?,
  vehicleType: $enumDecode(_$VehicleTypeEnumMap, json['vehicleType']),
  price: (json['price'] as num).toInt(),
  commissionRate: (json['commissionRate'] as num).toInt(),
  commissionAmount: (json['commissionAmount'] as num).toInt(),
  pickupTime: DateTime.parse(json['pickupTime'] as String),
  pickupAddress: json['pickupAddress'] as String,
  passengerZalo: json['passengerZalo'] as String,
  status:
      $enumDecodeNullable(_$RideStatusEnumMap, json['status']) ??
      RideStatus.waiting,
  isAsap: json['isAsap'] as bool? ?? false,
  province: json['province'] as String?,
  district: json['district'] as String?,
  commune: json['commune'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  creator: DriverModel.fromJson(json['creator'] as Map<String, dynamic>),
  acceptor: json['acceptor'] == null
      ? null
      : DriverModel.fromJson(json['acceptor'] as Map<String, dynamic>),
);

Map<String, dynamic> _$$RideWithDriversModelImplToJson(
  _$RideWithDriversModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'creatorId': instance.creatorId,
  'acceptorId': instance.acceptorId,
  'vehicleType': _$VehicleTypeEnumMap[instance.vehicleType]!,
  'price': instance.price,
  'commissionRate': instance.commissionRate,
  'commissionAmount': instance.commissionAmount,
  'pickupTime': instance.pickupTime.toIso8601String(),
  'pickupAddress': instance.pickupAddress,
  'passengerZalo': instance.passengerZalo,
  'status': _$RideStatusEnumMap[instance.status]!,
  'isAsap': instance.isAsap,
  'province': instance.province,
  'district': instance.district,
  'commune': instance.commune,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'creator': instance.creator,
  'acceptor': instance.acceptor,
};

_$CreateRideModelImpl _$$CreateRideModelImplFromJson(
  Map<String, dynamic> json,
) => _$CreateRideModelImpl(
  creatorId: json['creatorId'] as String,
  vehicleType: $enumDecode(_$VehicleTypeEnumMap, json['vehicleType']),
  price: (json['price'] as num).toInt(),
  commissionRate: (json['commissionRate'] as num).toInt(),
  pickupTime: DateTime.parse(json['pickupTime'] as String),
  pickupAddress: json['pickupAddress'] as String,
  passengerZalo: json['passengerZalo'] as String,
  isAsap: json['isAsap'] as bool? ?? false,
  province: json['province'] as String?,
  district: json['district'] as String?,
  commune: json['commune'] as String?,
);

Map<String, dynamic> _$$CreateRideModelImplToJson(
  _$CreateRideModelImpl instance,
) => <String, dynamic>{
  'creatorId': instance.creatorId,
  'vehicleType': _$VehicleTypeEnumMap[instance.vehicleType]!,
  'price': instance.price,
  'commissionRate': instance.commissionRate,
  'pickupTime': instance.pickupTime.toIso8601String(),
  'pickupAddress': instance.pickupAddress,
  'passengerZalo': instance.passengerZalo,
  'isAsap': instance.isAsap,
  'province': instance.province,
  'district': instance.district,
  'commune': instance.commune,
};
