import 'package:equatable/equatable.dart';
import '../../core/constants/enums.dart';

class Driver extends Equatable {
  const Driver({
    required this.id,
    required this.name,
    required this.phone,
    this.zalo,
    this.status = DriverStatus.active,
    this.currentLocation,
    this.rating = '0.00',
    this.totalRides = 0,
    this.createdAt,
  });

  final String id;
  final String name;
  final String phone;
  final String? zalo;
  final DriverStatus status;
  final String? currentLocation;
  final String rating;
  final int totalRides;
  final DateTime? createdAt;

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        zalo,
        status,
        currentLocation,
        rating,
        totalRides,
        createdAt,
      ];

  Driver copyWith({
    String? id,
    String? name,
    String? phone,
    String? zalo,
    DriverStatus? status,
    String? currentLocation,
    String? rating,
    int? totalRides,
    DateTime? createdAt,
  }) {
    return Driver(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      zalo: zalo ?? this.zalo,
      status: status ?? this.status,
      currentLocation: currentLocation ?? this.currentLocation,
      rating: rating ?? this.rating,
      totalRides: totalRides ?? this.totalRides,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class DriverWithStats extends Equatable {
  const DriverWithStats({
    required this.id,
    required this.name,
    required this.phone,
    this.zalo,
    this.status = DriverStatus.active,
    this.currentLocation,
    this.rating = '0.00',
    this.totalRides = 0,
    this.completedRides = 0,
    this.createdAt,
  });

  final String id;
  final String name;
  final String phone;
  final String? zalo;
  final DriverStatus status;
  final String? currentLocation;
  final String rating;
  final int totalRides;
  final int completedRides;
  final DateTime? createdAt;

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        zalo,
        status,
        currentLocation,
        rating,
        totalRides,
        completedRides,
        createdAt,
      ];
}

class CreateDriver extends Equatable {
  const CreateDriver({
    required this.name,
    required this.phone,
    this.zalo,
    this.status = DriverStatus.active,
    this.currentLocation,
  });

  final String name;
  final String phone;
  final String? zalo;
  final DriverStatus status;
  final String? currentLocation;

  @override
  List<Object?> get props => [
        name,
        phone,
        zalo,
        status,
        currentLocation,
      ];
}
