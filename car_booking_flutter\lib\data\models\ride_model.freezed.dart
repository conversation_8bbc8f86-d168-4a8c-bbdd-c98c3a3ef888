// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

RideModel _$RideModelFromJson(Map<String, dynamic> json) {
  return _RideModel.fromJson(json);
}

/// @nodoc
mixin _$RideModel {
  String get id => throw _privateConstructorUsedError;
  String get creatorId => throw _privateConstructorUsedError;
  String? get acceptorId => throw _privateConstructorUsedError;
  VehicleType get vehicleType => throw _privateConstructorUsedError;
  int get price => throw _privateConstructorUsedError;
  int get commissionRate => throw _privateConstructorUsedError;
  int get commissionAmount => throw _privateConstructorUsedError;
  DateTime get pickupTime => throw _privateConstructorUsedError;
  String get pickupAddress => throw _privateConstructorUsedError;
  String get passengerZalo => throw _privateConstructorUsedError;
  RideStatus get status => throw _privateConstructorUsedError;
  bool get isAsap => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  String? get district => throw _privateConstructorUsedError;
  String? get commune => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this RideModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RideModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideModelCopyWith<RideModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideModelCopyWith<$Res> {
  factory $RideModelCopyWith(RideModel value, $Res Function(RideModel) then) =
      _$RideModelCopyWithImpl<$Res, RideModel>;
  @useResult
  $Res call({
    String id,
    String creatorId,
    String? acceptorId,
    VehicleType vehicleType,
    int price,
    int commissionRate,
    int commissionAmount,
    DateTime pickupTime,
    String pickupAddress,
    String passengerZalo,
    RideStatus status,
    bool isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$RideModelCopyWithImpl<$Res, $Val extends RideModel>
    implements $RideModelCopyWith<$Res> {
  _$RideModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? creatorId = null,
    Object? acceptorId = freezed,
    Object? vehicleType = null,
    Object? price = null,
    Object? commissionRate = null,
    Object? commissionAmount = null,
    Object? pickupTime = null,
    Object? pickupAddress = null,
    Object? passengerZalo = null,
    Object? status = null,
    Object? isAsap = null,
    Object? province = freezed,
    Object? district = freezed,
    Object? commune = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            creatorId: null == creatorId
                ? _value.creatorId
                : creatorId // ignore: cast_nullable_to_non_nullable
                      as String,
            acceptorId: freezed == acceptorId
                ? _value.acceptorId
                : acceptorId // ignore: cast_nullable_to_non_nullable
                      as String?,
            vehicleType: null == vehicleType
                ? _value.vehicleType
                : vehicleType // ignore: cast_nullable_to_non_nullable
                      as VehicleType,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as int,
            commissionRate: null == commissionRate
                ? _value.commissionRate
                : commissionRate // ignore: cast_nullable_to_non_nullable
                      as int,
            commissionAmount: null == commissionAmount
                ? _value.commissionAmount
                : commissionAmount // ignore: cast_nullable_to_non_nullable
                      as int,
            pickupTime: null == pickupTime
                ? _value.pickupTime
                : pickupTime // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            pickupAddress: null == pickupAddress
                ? _value.pickupAddress
                : pickupAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            passengerZalo: null == passengerZalo
                ? _value.passengerZalo
                : passengerZalo // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as RideStatus,
            isAsap: null == isAsap
                ? _value.isAsap
                : isAsap // ignore: cast_nullable_to_non_nullable
                      as bool,
            province: freezed == province
                ? _value.province
                : province // ignore: cast_nullable_to_non_nullable
                      as String?,
            district: freezed == district
                ? _value.district
                : district // ignore: cast_nullable_to_non_nullable
                      as String?,
            commune: freezed == commune
                ? _value.commune
                : commune // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$RideModelImplCopyWith<$Res>
    implements $RideModelCopyWith<$Res> {
  factory _$$RideModelImplCopyWith(
    _$RideModelImpl value,
    $Res Function(_$RideModelImpl) then,
  ) = __$$RideModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String creatorId,
    String? acceptorId,
    VehicleType vehicleType,
    int price,
    int commissionRate,
    int commissionAmount,
    DateTime pickupTime,
    String pickupAddress,
    String passengerZalo,
    RideStatus status,
    bool isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$RideModelImplCopyWithImpl<$Res>
    extends _$RideModelCopyWithImpl<$Res, _$RideModelImpl>
    implements _$$RideModelImplCopyWith<$Res> {
  __$$RideModelImplCopyWithImpl(
    _$RideModelImpl _value,
    $Res Function(_$RideModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of RideModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? creatorId = null,
    Object? acceptorId = freezed,
    Object? vehicleType = null,
    Object? price = null,
    Object? commissionRate = null,
    Object? commissionAmount = null,
    Object? pickupTime = null,
    Object? pickupAddress = null,
    Object? passengerZalo = null,
    Object? status = null,
    Object? isAsap = null,
    Object? province = freezed,
    Object? district = freezed,
    Object? commune = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$RideModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        creatorId: null == creatorId
            ? _value.creatorId
            : creatorId // ignore: cast_nullable_to_non_nullable
                  as String,
        acceptorId: freezed == acceptorId
            ? _value.acceptorId
            : acceptorId // ignore: cast_nullable_to_non_nullable
                  as String?,
        vehicleType: null == vehicleType
            ? _value.vehicleType
            : vehicleType // ignore: cast_nullable_to_non_nullable
                  as VehicleType,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        commissionRate: null == commissionRate
            ? _value.commissionRate
            : commissionRate // ignore: cast_nullable_to_non_nullable
                  as int,
        commissionAmount: null == commissionAmount
            ? _value.commissionAmount
            : commissionAmount // ignore: cast_nullable_to_non_nullable
                  as int,
        pickupTime: null == pickupTime
            ? _value.pickupTime
            : pickupTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        pickupAddress: null == pickupAddress
            ? _value.pickupAddress
            : pickupAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        passengerZalo: null == passengerZalo
            ? _value.passengerZalo
            : passengerZalo // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as RideStatus,
        isAsap: null == isAsap
            ? _value.isAsap
            : isAsap // ignore: cast_nullable_to_non_nullable
                  as bool,
        province: freezed == province
            ? _value.province
            : province // ignore: cast_nullable_to_non_nullable
                  as String?,
        district: freezed == district
            ? _value.district
            : district // ignore: cast_nullable_to_non_nullable
                  as String?,
        commune: freezed == commune
            ? _value.commune
            : commune // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RideModelImpl implements _RideModel {
  const _$RideModelImpl({
    required this.id,
    required this.creatorId,
    this.acceptorId,
    required this.vehicleType,
    required this.price,
    required this.commissionRate,
    required this.commissionAmount,
    required this.pickupTime,
    required this.pickupAddress,
    required this.passengerZalo,
    this.status = RideStatus.waiting,
    this.isAsap = false,
    this.province,
    this.district,
    this.commune,
    this.createdAt,
    this.updatedAt,
  });

  factory _$RideModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideModelImplFromJson(json);

  @override
  final String id;
  @override
  final String creatorId;
  @override
  final String? acceptorId;
  @override
  final VehicleType vehicleType;
  @override
  final int price;
  @override
  final int commissionRate;
  @override
  final int commissionAmount;
  @override
  final DateTime pickupTime;
  @override
  final String pickupAddress;
  @override
  final String passengerZalo;
  @override
  @JsonKey()
  final RideStatus status;
  @override
  @JsonKey()
  final bool isAsap;
  @override
  final String? province;
  @override
  final String? district;
  @override
  final String? commune;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'RideModel(id: $id, creatorId: $creatorId, acceptorId: $acceptorId, vehicleType: $vehicleType, price: $price, commissionRate: $commissionRate, commissionAmount: $commissionAmount, pickupTime: $pickupTime, pickupAddress: $pickupAddress, passengerZalo: $passengerZalo, status: $status, isAsap: $isAsap, province: $province, district: $district, commune: $commune, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.acceptorId, acceptorId) ||
                other.acceptorId == acceptorId) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.commissionRate, commissionRate) ||
                other.commissionRate == commissionRate) &&
            (identical(other.commissionAmount, commissionAmount) ||
                other.commissionAmount == commissionAmount) &&
            (identical(other.pickupTime, pickupTime) ||
                other.pickupTime == pickupTime) &&
            (identical(other.pickupAddress, pickupAddress) ||
                other.pickupAddress == pickupAddress) &&
            (identical(other.passengerZalo, passengerZalo) ||
                other.passengerZalo == passengerZalo) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isAsap, isAsap) || other.isAsap == isAsap) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.district, district) ||
                other.district == district) &&
            (identical(other.commune, commune) || other.commune == commune) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    creatorId,
    acceptorId,
    vehicleType,
    price,
    commissionRate,
    commissionAmount,
    pickupTime,
    pickupAddress,
    passengerZalo,
    status,
    isAsap,
    province,
    district,
    commune,
    createdAt,
    updatedAt,
  );

  /// Create a copy of RideModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideModelImplCopyWith<_$RideModelImpl> get copyWith =>
      __$$RideModelImplCopyWithImpl<_$RideModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RideModelImplToJson(this);
  }
}

abstract class _RideModel implements RideModel {
  const factory _RideModel({
    required final String id,
    required final String creatorId,
    final String? acceptorId,
    required final VehicleType vehicleType,
    required final int price,
    required final int commissionRate,
    required final int commissionAmount,
    required final DateTime pickupTime,
    required final String pickupAddress,
    required final String passengerZalo,
    final RideStatus status,
    final bool isAsap,
    final String? province,
    final String? district,
    final String? commune,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$RideModelImpl;

  factory _RideModel.fromJson(Map<String, dynamic> json) =
      _$RideModelImpl.fromJson;

  @override
  String get id;
  @override
  String get creatorId;
  @override
  String? get acceptorId;
  @override
  VehicleType get vehicleType;
  @override
  int get price;
  @override
  int get commissionRate;
  @override
  int get commissionAmount;
  @override
  DateTime get pickupTime;
  @override
  String get pickupAddress;
  @override
  String get passengerZalo;
  @override
  RideStatus get status;
  @override
  bool get isAsap;
  @override
  String? get province;
  @override
  String? get district;
  @override
  String? get commune;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of RideModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideModelImplCopyWith<_$RideModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RideWithDriversModel _$RideWithDriversModelFromJson(Map<String, dynamic> json) {
  return _RideWithDriversModel.fromJson(json);
}

/// @nodoc
mixin _$RideWithDriversModel {
  String get id => throw _privateConstructorUsedError;
  String get creatorId => throw _privateConstructorUsedError;
  String? get acceptorId => throw _privateConstructorUsedError;
  VehicleType get vehicleType => throw _privateConstructorUsedError;
  int get price => throw _privateConstructorUsedError;
  int get commissionRate => throw _privateConstructorUsedError;
  int get commissionAmount => throw _privateConstructorUsedError;
  DateTime get pickupTime => throw _privateConstructorUsedError;
  String get pickupAddress => throw _privateConstructorUsedError;
  String get passengerZalo => throw _privateConstructorUsedError;
  RideStatus get status => throw _privateConstructorUsedError;
  bool get isAsap => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  String? get district => throw _privateConstructorUsedError;
  String? get commune => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DriverModel get creator => throw _privateConstructorUsedError;
  DriverModel? get acceptor => throw _privateConstructorUsedError;

  /// Serializes this RideWithDriversModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideWithDriversModelCopyWith<RideWithDriversModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideWithDriversModelCopyWith<$Res> {
  factory $RideWithDriversModelCopyWith(
    RideWithDriversModel value,
    $Res Function(RideWithDriversModel) then,
  ) = _$RideWithDriversModelCopyWithImpl<$Res, RideWithDriversModel>;
  @useResult
  $Res call({
    String id,
    String creatorId,
    String? acceptorId,
    VehicleType vehicleType,
    int price,
    int commissionRate,
    int commissionAmount,
    DateTime pickupTime,
    String pickupAddress,
    String passengerZalo,
    RideStatus status,
    bool isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
    DriverModel creator,
    DriverModel? acceptor,
  });

  $DriverModelCopyWith<$Res> get creator;
  $DriverModelCopyWith<$Res>? get acceptor;
}

/// @nodoc
class _$RideWithDriversModelCopyWithImpl<
  $Res,
  $Val extends RideWithDriversModel
>
    implements $RideWithDriversModelCopyWith<$Res> {
  _$RideWithDriversModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? creatorId = null,
    Object? acceptorId = freezed,
    Object? vehicleType = null,
    Object? price = null,
    Object? commissionRate = null,
    Object? commissionAmount = null,
    Object? pickupTime = null,
    Object? pickupAddress = null,
    Object? passengerZalo = null,
    Object? status = null,
    Object? isAsap = null,
    Object? province = freezed,
    Object? district = freezed,
    Object? commune = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? creator = null,
    Object? acceptor = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            creatorId: null == creatorId
                ? _value.creatorId
                : creatorId // ignore: cast_nullable_to_non_nullable
                      as String,
            acceptorId: freezed == acceptorId
                ? _value.acceptorId
                : acceptorId // ignore: cast_nullable_to_non_nullable
                      as String?,
            vehicleType: null == vehicleType
                ? _value.vehicleType
                : vehicleType // ignore: cast_nullable_to_non_nullable
                      as VehicleType,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as int,
            commissionRate: null == commissionRate
                ? _value.commissionRate
                : commissionRate // ignore: cast_nullable_to_non_nullable
                      as int,
            commissionAmount: null == commissionAmount
                ? _value.commissionAmount
                : commissionAmount // ignore: cast_nullable_to_non_nullable
                      as int,
            pickupTime: null == pickupTime
                ? _value.pickupTime
                : pickupTime // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            pickupAddress: null == pickupAddress
                ? _value.pickupAddress
                : pickupAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            passengerZalo: null == passengerZalo
                ? _value.passengerZalo
                : passengerZalo // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as RideStatus,
            isAsap: null == isAsap
                ? _value.isAsap
                : isAsap // ignore: cast_nullable_to_non_nullable
                      as bool,
            province: freezed == province
                ? _value.province
                : province // ignore: cast_nullable_to_non_nullable
                      as String?,
            district: freezed == district
                ? _value.district
                : district // ignore: cast_nullable_to_non_nullable
                      as String?,
            commune: freezed == commune
                ? _value.commune
                : commune // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            creator: null == creator
                ? _value.creator
                : creator // ignore: cast_nullable_to_non_nullable
                      as DriverModel,
            acceptor: freezed == acceptor
                ? _value.acceptor
                : acceptor // ignore: cast_nullable_to_non_nullable
                      as DriverModel?,
          )
          as $Val,
    );
  }

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverModelCopyWith<$Res> get creator {
    return $DriverModelCopyWith<$Res>(_value.creator, (value) {
      return _then(_value.copyWith(creator: value) as $Val);
    });
  }

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverModelCopyWith<$Res>? get acceptor {
    if (_value.acceptor == null) {
      return null;
    }

    return $DriverModelCopyWith<$Res>(_value.acceptor!, (value) {
      return _then(_value.copyWith(acceptor: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideWithDriversModelImplCopyWith<$Res>
    implements $RideWithDriversModelCopyWith<$Res> {
  factory _$$RideWithDriversModelImplCopyWith(
    _$RideWithDriversModelImpl value,
    $Res Function(_$RideWithDriversModelImpl) then,
  ) = __$$RideWithDriversModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String creatorId,
    String? acceptorId,
    VehicleType vehicleType,
    int price,
    int commissionRate,
    int commissionAmount,
    DateTime pickupTime,
    String pickupAddress,
    String passengerZalo,
    RideStatus status,
    bool isAsap,
    String? province,
    String? district,
    String? commune,
    DateTime? createdAt,
    DateTime? updatedAt,
    DriverModel creator,
    DriverModel? acceptor,
  });

  @override
  $DriverModelCopyWith<$Res> get creator;
  @override
  $DriverModelCopyWith<$Res>? get acceptor;
}

/// @nodoc
class __$$RideWithDriversModelImplCopyWithImpl<$Res>
    extends _$RideWithDriversModelCopyWithImpl<$Res, _$RideWithDriversModelImpl>
    implements _$$RideWithDriversModelImplCopyWith<$Res> {
  __$$RideWithDriversModelImplCopyWithImpl(
    _$RideWithDriversModelImpl _value,
    $Res Function(_$RideWithDriversModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? creatorId = null,
    Object? acceptorId = freezed,
    Object? vehicleType = null,
    Object? price = null,
    Object? commissionRate = null,
    Object? commissionAmount = null,
    Object? pickupTime = null,
    Object? pickupAddress = null,
    Object? passengerZalo = null,
    Object? status = null,
    Object? isAsap = null,
    Object? province = freezed,
    Object? district = freezed,
    Object? commune = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? creator = null,
    Object? acceptor = freezed,
  }) {
    return _then(
      _$RideWithDriversModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        creatorId: null == creatorId
            ? _value.creatorId
            : creatorId // ignore: cast_nullable_to_non_nullable
                  as String,
        acceptorId: freezed == acceptorId
            ? _value.acceptorId
            : acceptorId // ignore: cast_nullable_to_non_nullable
                  as String?,
        vehicleType: null == vehicleType
            ? _value.vehicleType
            : vehicleType // ignore: cast_nullable_to_non_nullable
                  as VehicleType,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        commissionRate: null == commissionRate
            ? _value.commissionRate
            : commissionRate // ignore: cast_nullable_to_non_nullable
                  as int,
        commissionAmount: null == commissionAmount
            ? _value.commissionAmount
            : commissionAmount // ignore: cast_nullable_to_non_nullable
                  as int,
        pickupTime: null == pickupTime
            ? _value.pickupTime
            : pickupTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        pickupAddress: null == pickupAddress
            ? _value.pickupAddress
            : pickupAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        passengerZalo: null == passengerZalo
            ? _value.passengerZalo
            : passengerZalo // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as RideStatus,
        isAsap: null == isAsap
            ? _value.isAsap
            : isAsap // ignore: cast_nullable_to_non_nullable
                  as bool,
        province: freezed == province
            ? _value.province
            : province // ignore: cast_nullable_to_non_nullable
                  as String?,
        district: freezed == district
            ? _value.district
            : district // ignore: cast_nullable_to_non_nullable
                  as String?,
        commune: freezed == commune
            ? _value.commune
            : commune // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        creator: null == creator
            ? _value.creator
            : creator // ignore: cast_nullable_to_non_nullable
                  as DriverModel,
        acceptor: freezed == acceptor
            ? _value.acceptor
            : acceptor // ignore: cast_nullable_to_non_nullable
                  as DriverModel?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RideWithDriversModelImpl implements _RideWithDriversModel {
  const _$RideWithDriversModelImpl({
    required this.id,
    required this.creatorId,
    this.acceptorId,
    required this.vehicleType,
    required this.price,
    required this.commissionRate,
    required this.commissionAmount,
    required this.pickupTime,
    required this.pickupAddress,
    required this.passengerZalo,
    this.status = RideStatus.waiting,
    this.isAsap = false,
    this.province,
    this.district,
    this.commune,
    this.createdAt,
    this.updatedAt,
    required this.creator,
    this.acceptor,
  });

  factory _$RideWithDriversModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideWithDriversModelImplFromJson(json);

  @override
  final String id;
  @override
  final String creatorId;
  @override
  final String? acceptorId;
  @override
  final VehicleType vehicleType;
  @override
  final int price;
  @override
  final int commissionRate;
  @override
  final int commissionAmount;
  @override
  final DateTime pickupTime;
  @override
  final String pickupAddress;
  @override
  final String passengerZalo;
  @override
  @JsonKey()
  final RideStatus status;
  @override
  @JsonKey()
  final bool isAsap;
  @override
  final String? province;
  @override
  final String? district;
  @override
  final String? commune;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DriverModel creator;
  @override
  final DriverModel? acceptor;

  @override
  String toString() {
    return 'RideWithDriversModel(id: $id, creatorId: $creatorId, acceptorId: $acceptorId, vehicleType: $vehicleType, price: $price, commissionRate: $commissionRate, commissionAmount: $commissionAmount, pickupTime: $pickupTime, pickupAddress: $pickupAddress, passengerZalo: $passengerZalo, status: $status, isAsap: $isAsap, province: $province, district: $district, commune: $commune, createdAt: $createdAt, updatedAt: $updatedAt, creator: $creator, acceptor: $acceptor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideWithDriversModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.acceptorId, acceptorId) ||
                other.acceptorId == acceptorId) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.commissionRate, commissionRate) ||
                other.commissionRate == commissionRate) &&
            (identical(other.commissionAmount, commissionAmount) ||
                other.commissionAmount == commissionAmount) &&
            (identical(other.pickupTime, pickupTime) ||
                other.pickupTime == pickupTime) &&
            (identical(other.pickupAddress, pickupAddress) ||
                other.pickupAddress == pickupAddress) &&
            (identical(other.passengerZalo, passengerZalo) ||
                other.passengerZalo == passengerZalo) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isAsap, isAsap) || other.isAsap == isAsap) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.district, district) ||
                other.district == district) &&
            (identical(other.commune, commune) || other.commune == commune) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.acceptor, acceptor) ||
                other.acceptor == acceptor));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    creatorId,
    acceptorId,
    vehicleType,
    price,
    commissionRate,
    commissionAmount,
    pickupTime,
    pickupAddress,
    passengerZalo,
    status,
    isAsap,
    province,
    district,
    commune,
    createdAt,
    updatedAt,
    creator,
    acceptor,
  ]);

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideWithDriversModelImplCopyWith<_$RideWithDriversModelImpl>
  get copyWith =>
      __$$RideWithDriversModelImplCopyWithImpl<_$RideWithDriversModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$RideWithDriversModelImplToJson(this);
  }
}

abstract class _RideWithDriversModel implements RideWithDriversModel {
  const factory _RideWithDriversModel({
    required final String id,
    required final String creatorId,
    final String? acceptorId,
    required final VehicleType vehicleType,
    required final int price,
    required final int commissionRate,
    required final int commissionAmount,
    required final DateTime pickupTime,
    required final String pickupAddress,
    required final String passengerZalo,
    final RideStatus status,
    final bool isAsap,
    final String? province,
    final String? district,
    final String? commune,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    required final DriverModel creator,
    final DriverModel? acceptor,
  }) = _$RideWithDriversModelImpl;

  factory _RideWithDriversModel.fromJson(Map<String, dynamic> json) =
      _$RideWithDriversModelImpl.fromJson;

  @override
  String get id;
  @override
  String get creatorId;
  @override
  String? get acceptorId;
  @override
  VehicleType get vehicleType;
  @override
  int get price;
  @override
  int get commissionRate;
  @override
  int get commissionAmount;
  @override
  DateTime get pickupTime;
  @override
  String get pickupAddress;
  @override
  String get passengerZalo;
  @override
  RideStatus get status;
  @override
  bool get isAsap;
  @override
  String? get province;
  @override
  String? get district;
  @override
  String? get commune;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DriverModel get creator;
  @override
  DriverModel? get acceptor;

  /// Create a copy of RideWithDriversModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideWithDriversModelImplCopyWith<_$RideWithDriversModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

CreateRideModel _$CreateRideModelFromJson(Map<String, dynamic> json) {
  return _CreateRideModel.fromJson(json);
}

/// @nodoc
mixin _$CreateRideModel {
  String get creatorId => throw _privateConstructorUsedError;
  VehicleType get vehicleType => throw _privateConstructorUsedError;
  int get price => throw _privateConstructorUsedError;
  int get commissionRate => throw _privateConstructorUsedError;
  DateTime get pickupTime => throw _privateConstructorUsedError;
  String get pickupAddress => throw _privateConstructorUsedError;
  String get passengerZalo => throw _privateConstructorUsedError;
  bool get isAsap => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  String? get district => throw _privateConstructorUsedError;
  String? get commune => throw _privateConstructorUsedError;

  /// Serializes this CreateRideModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateRideModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateRideModelCopyWith<CreateRideModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateRideModelCopyWith<$Res> {
  factory $CreateRideModelCopyWith(
    CreateRideModel value,
    $Res Function(CreateRideModel) then,
  ) = _$CreateRideModelCopyWithImpl<$Res, CreateRideModel>;
  @useResult
  $Res call({
    String creatorId,
    VehicleType vehicleType,
    int price,
    int commissionRate,
    DateTime pickupTime,
    String pickupAddress,
    String passengerZalo,
    bool isAsap,
    String? province,
    String? district,
    String? commune,
  });
}

/// @nodoc
class _$CreateRideModelCopyWithImpl<$Res, $Val extends CreateRideModel>
    implements $CreateRideModelCopyWith<$Res> {
  _$CreateRideModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateRideModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creatorId = null,
    Object? vehicleType = null,
    Object? price = null,
    Object? commissionRate = null,
    Object? pickupTime = null,
    Object? pickupAddress = null,
    Object? passengerZalo = null,
    Object? isAsap = null,
    Object? province = freezed,
    Object? district = freezed,
    Object? commune = freezed,
  }) {
    return _then(
      _value.copyWith(
            creatorId: null == creatorId
                ? _value.creatorId
                : creatorId // ignore: cast_nullable_to_non_nullable
                      as String,
            vehicleType: null == vehicleType
                ? _value.vehicleType
                : vehicleType // ignore: cast_nullable_to_non_nullable
                      as VehicleType,
            price: null == price
                ? _value.price
                : price // ignore: cast_nullable_to_non_nullable
                      as int,
            commissionRate: null == commissionRate
                ? _value.commissionRate
                : commissionRate // ignore: cast_nullable_to_non_nullable
                      as int,
            pickupTime: null == pickupTime
                ? _value.pickupTime
                : pickupTime // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            pickupAddress: null == pickupAddress
                ? _value.pickupAddress
                : pickupAddress // ignore: cast_nullable_to_non_nullable
                      as String,
            passengerZalo: null == passengerZalo
                ? _value.passengerZalo
                : passengerZalo // ignore: cast_nullable_to_non_nullable
                      as String,
            isAsap: null == isAsap
                ? _value.isAsap
                : isAsap // ignore: cast_nullable_to_non_nullable
                      as bool,
            province: freezed == province
                ? _value.province
                : province // ignore: cast_nullable_to_non_nullable
                      as String?,
            district: freezed == district
                ? _value.district
                : district // ignore: cast_nullable_to_non_nullable
                      as String?,
            commune: freezed == commune
                ? _value.commune
                : commune // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CreateRideModelImplCopyWith<$Res>
    implements $CreateRideModelCopyWith<$Res> {
  factory _$$CreateRideModelImplCopyWith(
    _$CreateRideModelImpl value,
    $Res Function(_$CreateRideModelImpl) then,
  ) = __$$CreateRideModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String creatorId,
    VehicleType vehicleType,
    int price,
    int commissionRate,
    DateTime pickupTime,
    String pickupAddress,
    String passengerZalo,
    bool isAsap,
    String? province,
    String? district,
    String? commune,
  });
}

/// @nodoc
class __$$CreateRideModelImplCopyWithImpl<$Res>
    extends _$CreateRideModelCopyWithImpl<$Res, _$CreateRideModelImpl>
    implements _$$CreateRideModelImplCopyWith<$Res> {
  __$$CreateRideModelImplCopyWithImpl(
    _$CreateRideModelImpl _value,
    $Res Function(_$CreateRideModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CreateRideModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creatorId = null,
    Object? vehicleType = null,
    Object? price = null,
    Object? commissionRate = null,
    Object? pickupTime = null,
    Object? pickupAddress = null,
    Object? passengerZalo = null,
    Object? isAsap = null,
    Object? province = freezed,
    Object? district = freezed,
    Object? commune = freezed,
  }) {
    return _then(
      _$CreateRideModelImpl(
        creatorId: null == creatorId
            ? _value.creatorId
            : creatorId // ignore: cast_nullable_to_non_nullable
                  as String,
        vehicleType: null == vehicleType
            ? _value.vehicleType
            : vehicleType // ignore: cast_nullable_to_non_nullable
                  as VehicleType,
        price: null == price
            ? _value.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        commissionRate: null == commissionRate
            ? _value.commissionRate
            : commissionRate // ignore: cast_nullable_to_non_nullable
                  as int,
        pickupTime: null == pickupTime
            ? _value.pickupTime
            : pickupTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        pickupAddress: null == pickupAddress
            ? _value.pickupAddress
            : pickupAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        passengerZalo: null == passengerZalo
            ? _value.passengerZalo
            : passengerZalo // ignore: cast_nullable_to_non_nullable
                  as String,
        isAsap: null == isAsap
            ? _value.isAsap
            : isAsap // ignore: cast_nullable_to_non_nullable
                  as bool,
        province: freezed == province
            ? _value.province
            : province // ignore: cast_nullable_to_non_nullable
                  as String?,
        district: freezed == district
            ? _value.district
            : district // ignore: cast_nullable_to_non_nullable
                  as String?,
        commune: freezed == commune
            ? _value.commune
            : commune // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateRideModelImpl implements _CreateRideModel {
  const _$CreateRideModelImpl({
    required this.creatorId,
    required this.vehicleType,
    required this.price,
    required this.commissionRate,
    required this.pickupTime,
    required this.pickupAddress,
    required this.passengerZalo,
    this.isAsap = false,
    this.province,
    this.district,
    this.commune,
  });

  factory _$CreateRideModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateRideModelImplFromJson(json);

  @override
  final String creatorId;
  @override
  final VehicleType vehicleType;
  @override
  final int price;
  @override
  final int commissionRate;
  @override
  final DateTime pickupTime;
  @override
  final String pickupAddress;
  @override
  final String passengerZalo;
  @override
  @JsonKey()
  final bool isAsap;
  @override
  final String? province;
  @override
  final String? district;
  @override
  final String? commune;

  @override
  String toString() {
    return 'CreateRideModel(creatorId: $creatorId, vehicleType: $vehicleType, price: $price, commissionRate: $commissionRate, pickupTime: $pickupTime, pickupAddress: $pickupAddress, passengerZalo: $passengerZalo, isAsap: $isAsap, province: $province, district: $district, commune: $commune)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateRideModelImpl &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.commissionRate, commissionRate) ||
                other.commissionRate == commissionRate) &&
            (identical(other.pickupTime, pickupTime) ||
                other.pickupTime == pickupTime) &&
            (identical(other.pickupAddress, pickupAddress) ||
                other.pickupAddress == pickupAddress) &&
            (identical(other.passengerZalo, passengerZalo) ||
                other.passengerZalo == passengerZalo) &&
            (identical(other.isAsap, isAsap) || other.isAsap == isAsap) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.district, district) ||
                other.district == district) &&
            (identical(other.commune, commune) || other.commune == commune));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    creatorId,
    vehicleType,
    price,
    commissionRate,
    pickupTime,
    pickupAddress,
    passengerZalo,
    isAsap,
    province,
    district,
    commune,
  );

  /// Create a copy of CreateRideModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateRideModelImplCopyWith<_$CreateRideModelImpl> get copyWith =>
      __$$CreateRideModelImplCopyWithImpl<_$CreateRideModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateRideModelImplToJson(this);
  }
}

abstract class _CreateRideModel implements CreateRideModel {
  const factory _CreateRideModel({
    required final String creatorId,
    required final VehicleType vehicleType,
    required final int price,
    required final int commissionRate,
    required final DateTime pickupTime,
    required final String pickupAddress,
    required final String passengerZalo,
    final bool isAsap,
    final String? province,
    final String? district,
    final String? commune,
  }) = _$CreateRideModelImpl;

  factory _CreateRideModel.fromJson(Map<String, dynamic> json) =
      _$CreateRideModelImpl.fromJson;

  @override
  String get creatorId;
  @override
  VehicleType get vehicleType;
  @override
  int get price;
  @override
  int get commissionRate;
  @override
  DateTime get pickupTime;
  @override
  String get pickupAddress;
  @override
  String get passengerZalo;
  @override
  bool get isAsap;
  @override
  String? get province;
  @override
  String? get district;
  @override
  String? get commune;

  /// Create a copy of CreateRideModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateRideModelImplCopyWith<_$CreateRideModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
